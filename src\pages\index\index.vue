<template>

  <view v-if="currentTab === 'index'">
    <uni-notice-bar show-icon scrollable
      text="'宜侬小程序上线了！📣📣📣宜侬工厂内购群1️⃣邀请好友5人进群！！🎁赠送价值169元✓悦木集蜂蜜面膜1瓶2️⃣邀请好友10人进群！！🎁赠送价值699元✓悦木集悦享礼盒1套！'" />

    <uni-swiper-dot @clickItem=clickItem :info="swiperList" :current="currentSwipper">
      <swiper class="swiper-box" @change=swiperChange :current="currentSwipper">
        <swiper-item v-for="(item, index) in swiperList" class="" :key="index">
          <video v-if="item.type === 'video'" :src="item.url" style="width: 750rpx;" />
          <image v-if="item.type === 'image'" :src="item.url" style="width: 750rpx;" />
        </swiper-item>
      </swiper>
    </uni-swiper-dot>

    <view class="my-wrapper">
      <uni-title type="h3" title="我的" />

      <uni-row :gutter="20">
        <uni-col :span="12">
          <view class="left" @click="navTo('/pages/index/apply')">
            <image src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240918/1726643125032.png" mode="widthFix"
              style="width: 160rpx;" />
            <text class="label">申请</text>
          </view>
        </uni-col>
        <uni-col :span="12">
          <view class="item" @click="navTo('/pages/index/audit')">
            <view>
              <image src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240918/1726643125094.png" mode="widthFix"
                style="width: 160rpx;" />
            </view>
            <text class="label">审核</text>
          </view>
          <view class="item" @click="navTo('/pages/index/todo')">
            <view>
              <image src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240918/1726643125116.png" mode="widthFix"
                style="width: 160rpx;" />
            </view>
            <uni-badge class="uni-badge-left-margin" :text="toDoCount" absolute="rightTop" size="small">
              <text class="label">代办</text>
            </uni-badge>
          </view>
        </uni-col>
      </uni-row>
    </view>

    <view class="oa-wrapper">
      <uni-title type="h3" title="办公协同" />

      <uni-grid>
        <uni-grid-item>
          <view class="grid-item" @click="navTo('/pagesCustomer/index')">
            <image src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240620/1718869259085.png" mode="widthFix"
              style="width: 120rpx;" />
            <text class="label">客户</text>
          </view>
        </uni-grid-item>
        <uni-grid-item v-if="hasPerms('project:project:list')">
          <view class="grid-item" @click="navTo('/pages/project/tabbar')">
            <image src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240620/1718869420311.png" mode="widthFix"
              style="width: 120rpx;" />
            <text class="label">项目</text>
          </view>
        </uni-grid-item>
        <uni-grid-item>
          <view class="grid-item" @click="navTo('/pages/resource/tabbar')">
            <image src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240620/1718869259468.png" mode="widthFix"
              style="width: 120rpx;" />
            <text class="label">资源库</text>
          </view>
        </uni-grid-item>
        <!-- <uni-grid-item>
        <view class="grid-item" @click="navTo('/pages/index/web')" >
          <image
            src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240613/1718239172137.png"
            mode="widthFix"
            style="width: 120rpx;"
          />
          <text class="label">研发</text>
        </view>
      </uni-grid-item> -->
        <!-- <uni-grid-item>
        <view class="grid-item">
          <image
            src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240613/1718239172137.png"
            mode="widthFix"
            style="width: 120rpx;"
          />
          <text class="label">研发</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item">
          <image
            src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240620/1718869259360.png"
            mode="widthFix"
            style="width: 120rpx;"
          />
          <text class="label">品质</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item" >
          <image
            src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240620/1718869605174.png"
            mode="widthFix"
            style="width: 120rpx;"
          />
          <text class="label">生产</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item">
          <image
            src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240918/1726643125031.png"
            mode="widthFix"
            style="width: 120rpx;"
          />
          <text class="label">人事</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item">
          <image
            src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240613/1718239119158.png"
            mode="widthFix"
            style="width: 120rpx;"
          />
          <text class="label">行政</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item">
          <image
            src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240620/1718869259217.png"
            mode="widthFix"
            style="width: 120rpx;"
          />
          <text class="label">法务</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item" @click="navTo('/pages/sop/tabbar')" >
          <image
            src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240620/1718869259074.png"
            mode="widthFix"
            style="width: 120rpx;"
          />
          <text class="label">工艺</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item">
          <image
            src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240620/1718869605184.png"
            mode="widthFix"
            style="width: 120rpx;"
          />
          <text class="label">财务</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item">
          <image
            src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240620/1718869259362.png"
            mode="widthFix"
            style="width: 120rpx;"
          />
          <text class="label">供应链</text>
        </view>
      </uni-grid-item> -->
      </uni-grid>
    </view>

    <view class="footer-wrapper">
      <view class="name">上海瀛彩生物科技有限公司</view>
      <view class="text">地 址：上海市奉贤区金汇镇堂富路150号</view>
      <view class="text">联系人：余磊</view>
      <view class="text">电 话：18994566699</view>
      <view class="text">投 诉 邮 箱：<EMAIL></view>
      <view class="text">邮 政 编 码：201400</view>
      <view class="text">传 真：86-021-57486255</view>
    </view>
  </view>
  <view v-if="currentTab === 'my'">
    <button @click="logOut">退出</button>
  </view>

  <up-tabbar :value="currentTab" @change="tabChange">
    <up-tabbar-item text="首页" icon="info-circle" name="index" />
    <up-tabbar-item text="我的" icon="clock" name="my" />
  </up-tabbar>

  <!-- <uni-fab ref="fab" :content="content" :@trigger="trigger" /> -->

  <up-toast ref="uToastRef" />
</template>

<script setup lang="ts">
import { onLoad, onPullDownRefresh } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';
import { useAuthStore } from '@/state/modules/user';
import type { Notice } from '@/types/system/notice';
import { navTo } from '@/utils/tools';
import { hasPerms } from "@/utils/tools";

const authStore = useAuthStore();
const currentTab = ref('index')
const swiperList = reactive([
  {
    url: "https://enow.oss-cn-beijing.aliyuncs.com/images/20250802/1754116503580.jpg",
    type: "image",
  },
  {
    url: "https://enow.oss-cn-beijing.aliyuncs.com/images/20240517/1715923694285.mp4",
    type: "video",
  },
  {
    url: "https://enow.oss-cn-beijing.aliyuncs.com/images/20240518/1715994776853.mp4",
    type: "video",
  },
]);
const uToastRef = ref()
const noticeList = reactive<String[]>([])
const currentSwipper = ref()
const toDoCount = ref<Number>(0)
const content = reactive([
  {
    iconPath: '/static/image.png',
    selectedIconPath: '/static/image-active.png',
    text: '相册',
    active: false
  },
  {
    iconPath: '/static/home.png',
    selectedIconPath: '/static/home-active.png',
    text: '首页',
    active: false
  }
])

onLoad(async () => {
  if (!uni.getStorageSync('dict_data')) {
    let dictRes = await uni.$u.http.get('/system/dict/all')
    uni.setStorageSync('dict_data', dictRes)
    uni.reLaunch({ url: '/pages/index/index' })
  }
  await init();
})
onPullDownRefresh(async () => {
  await init();
  uni.stopPullDownRefresh();
});

const logOut = async () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        await authStore.layout();
        uni.reLaunch({ url: '/pages/login/index' });
      }
    }
  });
}
const trigger = (e: any) => {
  content[e.index].active = !e.item.active
  uni.showModal({
    title: '提示',
    content: `您${content[e.index].active ? '选中了' : '取消了'}${e.item.text}`,
    success: function (res) {
      if (res.confirm) {
        console.log('用户点击确定')
      } else if (res.cancel) {
        console.log('用户点击取消')
      }
    }
  })
}
const tabChange = (name: string) => {
  currentTab.value = name
}
const init = async () => {
  let badgeObj = await uni.$u.http.get('/index/badge')
  if (badgeObj) {
    if (badgeObj.sumCount) {
      toDoCount.value = badgeObj.sumCount
    }
  }
  noticeList.length = 0

  // const noticeRes:Notice[] = await uni.$u.http.get('/system/notice/allByDept')
  // console.log(noticeRes)
}
const swiperChange = async (e: any) => {
  currentSwipper.value = e.detail.current
}
const clickItem = async (e: any) => {
  currentSwipper.value = e
}
</script>
<script lang="ts">
export default {
  options: {
    styleIsolation: "shared"
  },
}
</script>
<style lang="scss" scoped>
::v-deep .uni-noticebar {
  margin-bottom: 0 !important;
}

.uni-badge-left-margin {
  margin-left: 10px;
}

.my-wrapper {
  padding: 10rpx 20rpx;

  .label {
    font-size: small;
    color: $uni-main-color;
  }

  .left {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 336rpx;
  }

  .item {
    display: flex;
    align-items: center;
    justify-content: center;
  }

}

.oa-wrapper {
  padding: 10rpx 20rpx;

  .grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;

    .label {
      margin-top: 10rpx;
      font-size: $font-sm;
    }
  }

}

.swiper-box {
  height: 450rpx;

  .item {
    height: 450rpx;
  }
}

.footer-wrapper {
  background: #373D53;
  color: #fff;
  height: 300rpx;
  padding: 10rpx 20rpx;

  .name {
    font-size: $font-base;
    margin: 20rpx 0;
  }

  .text {
    font-size: $font-sm;
    margin-top: 5rpx;
  }
}
</style>
