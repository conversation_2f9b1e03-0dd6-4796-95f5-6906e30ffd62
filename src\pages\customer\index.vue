<template>
    <view class="page-wrapper">

        <view v-show="showSearch" class="form-wrapper">
            <uni-forms :modelValue="queryParams">
                <uni-forms-item label="客户代码" name="code">
                    <uni-easyinput trim="both" v-model="queryParams.code" />
                </uni-forms-item>
                <uni-forms-item label="客户名称" name="name">
                    <uni-easyinput trim="both" v-model="queryParams.name" />
                </uni-forms-item>
                <uni-forms-item label="客户级别" name="customerLevel">
                    <uni-data-select v-model="queryParams.customerLevel" :localdata="customerLevelOptions" />
                </uni-forms-item>
                <uni-forms-item label="客户状态" name="customerStatus">
                    <uni-data-select v-model="queryParams.customerStatus" :localdata="customerStatusOptions" />
                </uni-forms-item>
            </uni-forms>
            <button @click="handleQuery" size="mini">查询</button>
            <button @click="resetQuery" size="mini">重置</button>
        </view>

        <view class="table-tools">
            <view>
                <text class="ali-icon ali-tianjia" @click="navTo('/pages/customer/save')" />
            </view>
            <view class="btn-wrapper">
                <uni-icons type="search" @click="showSearch = !showSearch" />
                <button :disabled="btnLoading" :loading="btnLoading" @click="handleQuery" size="mini" plain>
                    <uni-icons type="reload" />
                </button>
            </view>
        </view>

        <uni-table border stripe emptyText="暂无更多数据">
            <uni-tr>
                <uni-th width="80" align="center">客户代码</uni-th>
                <uni-th>客户名称</uni-th>
                <uni-th>客户级别</uni-th>
                <uni-th>客户状态</uni-th>
                <uni-th>客户类型</uni-th>
                <uni-th>客户来源</uni-th>
                <uni-th>所属区域</uni-th>
                <uni-th>销售渠道</uni-th>
                <uni-th>业务</uni-th>
                <uni-th width="50" align="center">操作</uni-th>
            </uni-tr>
            <uni-tr v-for="item in dataArray" :key="item.id">
                <uni-td>{{ item.code }}</uni-td>
                <uni-td>
                    <text style="color: #2979ff"
                        @click="navTo('/pages/customer/save?id=' + item.id + '&readonly=' + true)">{{ item.name
                        }}</text>
                </uni-td>
                <uni-td>{{ getDictLabel('CUSTOMER_LEVEL', item.customerLevel) }}</uni-td>
                <uni-td>{{ getDictLabel('CUSTOMER_STATUS', item.customerStatus) }}</uni-td>
                <uni-td>{{ item.customerType }}</uni-td>
                <uni-td>{{ item.resource }}</uni-td>
                <uni-td>{{ getDictLabel('CUSTOMER_AREA', item.customerArea) }}</uni-td>
                <uni-td>{{ getDictLabels('CUSTOMER_SALES', item.customerSales) }}</uni-td>
                <uni-td>{{ item.customerYw }}</uni-td>
                <uni-td>
                    <text class="ali-icon ali-bianji" @click="navTo('/pages/customer/save?id=' + item.id)" />
                    <!-- <uni-icons type="list" @click="navTo('/pages/customer/grid?id=' + item.id + '&name=' + (item.shortName ? item.shortName : item.name))" /> -->
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-pagination :current="queryParams.pageNum" :total="total" :page-size="queryParams.pageSize"
            @change="pageChange" />

        <up-toast ref="uToastRef" />
        <up-loading-page :loading="loading" />
    </view>
</template>
<script lang="ts" setup>
import type { Customer } from '@/types/customer';
import { getDictLabel, getDictData, getDictLabels, navTo } from '@/utils/tools';
import { onLoad, onPullDownRefresh, onUnload } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';

const queryParams = reactive<any>({
    pageNum: 1,
    pageSize: 10,
    code: '',
    name: '',
    customerLevel: '',
    customerStatus: '',
})
let loadmoreStatus = ref('loadmore')
const dataArray: Customer[] = reactive([])
const loading = ref(false)
const btnLoading = ref(false)
const showSearch = ref(false)
const uToastRef = ref()
const total = ref<Number>(0)
const customerLevelOptions = getDictData('CUSTOMER_LEVEL')
const customerStatusOptions = getDictData('CUSTOMER_STATUS')

onLoad(async () => {
    uni.$on('refreshCustomer', getList)
    await resetQuery()
})
onUnload(async () => {
    uni.$off('refreshCustomer')
})
onPullDownRefresh(async () => {
    await resetQuery();
    uni.stopPullDownRefresh();
});

const pageChange = async (e: Object) => {
    queryParams.pageNum = e.current
    await getList()
}
const handleQuery = async () => {
    dataArray.length = 0
    queryParams.pageNum = 1
    await getList()
}
const resetQuery = async () => {
    queryParams.code = ''
    queryParams.name = ''
    queryParams.customerLevel = ''
    queryParams.customerStatus = ''
    await handleQuery()
}
const getList = async () => {
    const params = Object.assign({}, queryParams)
    loadmoreStatus.value = 'loading'
    loading.value = true
    let projectRes = await uni.$u.http.get('/customer/list', { params })
    loadmoreStatus.value = 'loadmore'
    loading.value = false
    dataArray.length = 0
    dataArray.push(...projectRes.rows)
    total.value = projectRes.total
    if (projectRes.length < queryParams.pageSize) {
        loadmoreStatus.value = 'nomore'
    }
}
</script>
<script lang="ts">
export default {
    options: {
        styleIsolation: "shared"
    },
}
</script>
<style lang="scss" scoped>
::v-deep .uni-table {
    .uni-table-tr {
        .uni-table-th {
            background-color: rgba(248, 248, 249, 1)
        }

        .uni-table-th:last-child,
        .uni-table-td:last-child {
            position: sticky;
            right: 0;
            background-color: rgba(248, 248, 249, 1);
            z-index: 1;
        }

    }
}
</style>