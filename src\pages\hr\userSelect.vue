<template>
    <view class="page-wrapper">

        <view v-show="showSearch" class="form-wrapper">
            <uni-forms :modelValue="queryParams">
                <uni-forms-item label="用户名" name="nickName">
                    <uni-easyinput trim="both" v-model="queryParams.nickName" />
                </uni-forms-item>
            </uni-forms>
            <button @click="handleQuery" size="mini">查询</button>
            <button @click="resetQuery" size="mini">重置</button>
        </view>

        <view class="table-tools">
            <view>
            </view>
            <view class="btn-wrapper">
                <uni-icons type="search" @click="showSearch = !showSearch" />
                <button :disabled="btnLoading" :loading="btnLoading" @click="handleQuery" size="mini" plain>
                    <uni-icons type="reload" />
                </button>
            </view>
        </view>

        <uni-table border stripe emptyText="暂无更多数据">
            <uni-tr>
                <uni-th width="100" align="center">员工编号</uni-th>
                <uni-th width="180" align="center">员工姓名</uni-th>
            </uni-tr>
            <uni-tr v-for="item in dataArray" :key="item.userId">
                <uni-td><text @click="selectRow(item)" style="color: #2979ff;">{{ item.userCode }}</text></uni-td>
                <uni-td><text @click="selectRow(item)" style="color: #2979ff;">{{ item.nickName }}</text></uni-td>
            </uni-tr>
        </uni-table>

        <uni-pagination :current="queryParams.pageNum" :total="total" :page-size="queryParams.pageSize"
            @change="pageChange" />

        <up-toast ref="uToastRef" />
        <up-loading-page :loading="loading" />
    </view>
</template>
<script lang="ts" setup>
import type { SysHrUser } from '@/types/hr/sysHrUser';
import { onLoad, onUnload, onPullDownRefresh } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';
import { useAuthStore } from '@/state/modules/user';

const authStore = useAuthStore();
const queryParams = reactive<any>({
    pageNum: 1,
    pageSize: 10,
    typeTreeLabel: '',
})
let loadmoreStatus = ref('loadmore')
const dataArray: SysHrUser[] = reactive([])
const loading = ref(false)
const btnLoading = ref(false)
const showSearch = ref(false)
const uToastRef = ref()
const total = ref<Number>(0)
const currentRow = ref<SysHrUser | null>()

onLoad(async () => {
    await resetQuery()
})
onUnload(async () => {
})
onPullDownRefresh(async () => {
    await resetQuery();
    uni.stopPullDownRefresh();
});

const selectRow = (item: SysHrUser) => {
    uni.$emit('selectHrUser', item)
    uni.navigateBack()
}
const pageChange = async (e: Object) => {
    queryParams.pageNum = e.current
    await getList()
}
const handleQuery = async () => {
    dataArray.length = 0
    queryParams.pageNum = 1
    await getList()
}
const resetQuery = async () => {
    await handleQuery()
}
const getList = async () => {
    const params = Object.assign({}, queryParams)
    params.userId = authStore.gxUser.id
    loadmoreStatus.value = 'loading'
    loading.value = true
    let logRes = await uni.$u.http.get('/hr/user/list', { params })
    loadmoreStatus.value = 'loadmore'
    loading.value = false
    dataArray.length = 0
    dataArray.push(...logRes.rows)
    total.value = logRes.total
    if (logRes.length < queryParams.pageSize) {
        loadmoreStatus.value = 'nomore'
    }
}
</script>
<script lang="ts">
export default {
    options: {
        styleIsolation: "shared"
    },
}
</script>
<style lang="scss" scoped>
::v-deep .uni-table {
    .uni-table-tr {
        .uni-table-th {
            background-color: rgba(248, 248, 249, 1)
        }

        .uni-table-th:first-child,
        .uni-table-td:first-child {
            position: sticky;
            left: 0;
            background-color: rgba(248, 248, 249, 1);
            z-index: 1;
        }

        .uni-table-th:last-child,
        .uni-table-td:last-child {
            position: sticky;
            right: 0;
            background-color: rgba(248, 248, 249, 1);
            z-index: 1;
        }

    }
}
</style>