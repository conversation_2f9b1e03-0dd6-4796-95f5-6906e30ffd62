<template>
    <view v-if="currentTab === 'mould'">
        <Mold />
    </view>
    <view v-if="currentTab === 'bc'">
        <Bc />
    </view>
    <view v-if="currentTab === 'finish'">
        <Finished />
    </view>
    <view v-if="currentTab === 'exhibits'">
        <Exhibits />
    </view>

    <up-tabbar :value="currentTab" @change="tabChange">
        <up-tabbar-item text="模具" icon="info-circle" name="mould" />
        <up-tabbar-item text="包材" icon="clock" name="bc" />
        <up-tabbar-item text="成品" icon="facebook" name="finish" />
        <up-tabbar-item text="展厅" icon="moments" name="exhibits" />
    </up-tabbar>
</template>
<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
import Bc from './bc/index.vue'
import Exhibits from './exhibits/index.vue'
import Finished from './finished/index.vue'
import Mold from './mold/index.vue'

const currentTab = ref('exhibits')

onLoad(() => {

})

const tabChange = (name: string) => {
    currentTab.value = name
}

</script>
<style lang="scss" scoped></style>