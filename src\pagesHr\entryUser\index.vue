<template>
    <view class="page-wrapper">

        <view v-show="showSearch" class="form-wrapper">
            <uni-forms :modelValue="queryParams">
                <uni-forms-item label="姓名" name="userName">
                    <uni-easyinput trim="both" v-model="queryParams.userName" />
                </uni-forms-item>
                <uni-forms-item label="应聘岗位" name="applicationPosition">
                    <uni-easyinput trim="both" v-model="queryParams.applicationPosition" />
                </uni-forms-item>
                <uni-forms-item label="性别" name="sex">
                    <uni-data-checkbox v-model="queryParams.sex" :localdata="userSexOptions" />
                </uni-forms-item>
                <uni-forms-item label="手机号" name="phonenumber">
                    <uni-easyinput trim="both" v-model="queryParams.phonenumber" type="number" />
                </uni-forms-item>
                <uni-forms-item label="户籍地址" name="hukouHjdz">
                    <uni-easyinput trim="both" v-model="queryParams.hukouHjdz" type="textarea" />
                </uni-forms-item>
                <uni-forms-item label="最高学历" name="degree">
                    <uni-data-select v-model="queryParams.degree" :localdata="degreeOptions" />
                </uni-forms-item>
                <uni-forms-item label="教育类别" name="degree">
                    <uni-data-select v-model="queryParams.firstDegree" :localdata="firstDegreeOptions" />
                </uni-forms-item>
            </uni-forms>
            <button @click="handleQuery" size="mini">查询</button>
            <button @click="resetQuery" size="mini">重置</button>
        </view>

        <view class="table-tools">
            <view>
                <!-- <text class="ali-icon ali-tianjia" /> -->
            </view>
            <view class="btn-wrapper">
                <uni-icons type="search" @click="showSearch = !showSearch" />
                <button :disabled="btnLoading" :loading="btnLoading" @click="handleQuery" size="mini" plain>
                    <uni-icons type="reload" />
                </button>
            </view>
        </view>

        <uni-table border stripe emptyText="暂无更多数据">
            <uni-tr>
                <uni-th width="120" align="center">面试日期</uni-th>
                <uni-th width="80" align="center">姓名</uni-th>
                <uni-th>应聘岗位</uni-th>
                <uni-th>性别</uni-th>
                <uni-th>手机号</uni-th>
                <uni-th>户籍地址</uni-th>
                <uni-th>最高学历</uni-th>
                <uni-th>教育类别</uni-th>
                <uni-th width="50" align="center">操作</uni-th>
            </uni-tr>
            <uni-tr v-for="item in dataArray" :key="item.id">
                <uni-td>{{ item.interViewDate }}</uni-td>
                <uni-td>{{ item.userName }}</uni-td>
                <uni-td>{{ item.applicationPosition }}</uni-td>
                <uni-td>{{ getDictLabel('sys_user_sex', item.sex) }}</uni-td>
                <uni-td>{{ item.phonenumber }}</uni-td>
                <uni-td>{{ item.hukouHjdz }}</uni-td>
                <uni-td>{{ getDictLabel('sys_user_degree', item.degree) }}</uni-td>
                <uni-td>{{ getDictLabel('sys_user_first_degree', item.firstDegree) }}</uni-td>
                <uni-td>
                    <text class="ali-icon ali-bianji" @click="navTo('/pagesHr/entryUser/log?id=' + item.id)" />
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-pagination :current="queryParams.pageNum" :total="total" :page-size="queryParams.pageSize"
            @change="pageChange" />

        <up-toast ref="uToastRef" />
        <up-loading-page :loading="loading" />
    </view>
</template>
<script lang="ts" setup>
import type { EntryUserLog } from '@/types/hr/entryUserLog';
import { getDictLabel, navTo, getDictData, } from '@/utils/tools';
import { onLoad, onUnload, onPullDownRefresh } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';

const queryParams = reactive<any>({
    pageNum: 1,
    pageSize: 10,
    userName: null,
    applicationPosition: null,
    sex: null,
    phonenumber: null,
    hukouHjdz: null,
    degree: null,
    firstDegree: null,
})
let loadmoreStatus = ref('loadmore')
const dataArray: EntryUserLog[] = reactive([])
const loading = ref(false)
const btnLoading = ref(false)
const showSearch = ref(false)
const uToastRef = ref()
const total = ref<Number>(0)
const userSexOptions = getDictData('sys_user_sex')
const degreeOptions = getDictData('sys_user_degree')
const firstDegreeOptions = getDictData('sys_user_first_degree')

onLoad(async () => {
    uni.$on('refreshEntryUserLog', getList)
    await resetQuery()
})
onUnload(async () => {
    uni.$off('refreshEntryUserLog')
})
onPullDownRefresh(async () => {
    await resetQuery();
    uni.stopPullDownRefresh();
});

const pageChange = async (e: Object) => {
    queryParams.pageNum = e.current
    await getList()
}
const handleQuery = async () => {
    dataArray.length = 0
    queryParams.pageNum = 1
    await getList()
}
const resetQuery = async () => {
    queryParams.customerName = ''
    queryParams.projectNo = ''
    await handleQuery()
}
const getList = async () => {
    const params = Object.assign({}, queryParams)
    loadmoreStatus.value = 'loading'
    loading.value = true
    let logRes = await uni.$u.http.get('/entry/user/log/list', { params })
    loadmoreStatus.value = 'loadmore'
    loading.value = false
    dataArray.length = 0
    dataArray.push(...logRes.rows)
    total.value = logRes.total
    if (logRes.length < queryParams.pageSize) {
        loadmoreStatus.value = 'nomore'
    }
}
</script>
<script lang="ts">
export default {
    options: {
        styleIsolation: "shared"
    },
}
</script>
<style lang="scss" scoped>
::v-deep .uni-table {
    .uni-table-tr {
        .uni-table-th {
            background-color: rgba(248, 248, 249, 1)
        }

        .uni-table-th:first-child,
        .uni-table-td:first-child {
            position: sticky;
            left: 0;
            background-color: rgba(248, 248, 249, 1);
            z-index: 1;
        }

        .uni-table-th:last-child,
        .uni-table-td:last-child {
            position: sticky;
            right: 0;
            background-color: rgba(248, 248, 249, 1);
            z-index: 1;
        }

    }
}
</style>