<template>
    <view class="page-wrapper">

        <view v-show="showSearch" class="form-wrapper">
            <uni-forms :modelValue="queryParams">
                <uni-forms-item label="客户姓名" name="name">
                    <uni-easyinput trim="both" v-model="queryParams.name" />
                </uni-forms-item>
                <uni-forms-item label="手机号" name="phone">
                    <uni-easyinput trim="both" v-model="queryParams.phone" />
                </uni-forms-item>
                <uni-forms-item label="公司" name="companyName">
                    <uni-easyinput trim="both" v-model="queryParams.companyName" />
                </uni-forms-item>
            </uni-forms>
            <button @click="handleQuery" size="mini">查询</button>
            <button @click="resetQuery" size="mini">重置</button>
        </view>

        <view class="table-tools">
            <view>
                <text class="ali-icon ali-tianjia" @click="navTo('/pagesCustomer/visit/save')" />
            </view>
            <view class="btn-wrapper">
                <uni-icons type="search" @click="showSearch = !showSearch" />
                <button :disabled="btnLoading" :loading="btnLoading" @click="handleQuery" size="mini" plain>
                    <uni-icons type="reload" />
                </button>
            </view>
        </view>

        <uni-table border stripe emptyText="暂无更多数据">
            <uni-tr>
                <uni-th width="80" align="center">申请人</uni-th>
                <uni-th width="120">客户姓名</uni-th>
                <uni-th width="120">手机号</uni-th>
                <uni-th width="100">随行人数</uni-th>
                <uni-th width="120">公司</uni-th>
                <uni-th width="80">二维码</uni-th>
                <uni-th width="120">预约访问时间</uni-th>
                <uni-th width="120">车辆信息</uni-th>
                <uni-th width="120">访问事由</uni-th>
                <uni-th width="50" align="center">操作</uni-th>
            </uni-tr>
            <uni-tr v-for="item in dataArray" :key="item.id">
                <uni-td>{{ item.createBy }}</uni-td>
                <uni-td>
                    <text style="color: #2979ff"
                        @click="navTo('/pagesCustomer/visit/save?id=' + item.id + '&readonly=' + true)">{{ item.name
                        }}</text>
                </uni-td>
                <uni-td>{{ item.phone }}</uni-td>
                <uni-td>{{ item.companionCount }}</uni-td>
                <uni-td>{{ item.companyName }}</uni-td>
                <uni-td>
                    <image v-if="item.qrCode" style="width: 120rpx" :src="item.qrCode" mode="widthFix"
                        @click="previewImg(item.qrCode)" />
                </uni-td>
                <uni-td>{{ item.scheduleTime }}</uni-td>
                <uni-td>{{ item.checkinTime }}</uni-td>
                <uni-td>{{ item.carInfo }}</uni-td>
                <uni-td>{{ item.purpose }}</uni-td>
                <uni-td>
                    <text class="ali-icon ali-bianji" @click="navTo('/pagesCustomer/visit/save?id=' + item.id)" />
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-pagination :current="queryParams.pageNum" :total="total" :page-size="queryParams.pageSize"
            @change="pageChange" />

        <up-toast ref="uToastRef" />
        <up-loading-page :loading="loading" />
    </view>
</template>
<script lang="ts" setup>
import type { CustomerVisit } from '@/types/customer/customerVisit';
import { navTo, previewImg } from '@/utils/tools';
import { onLoad, onPullDownRefresh, onUnload } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';

const queryParams = reactive<any>({
    pageNum: 1,
    pageSize: 10,
    name: '',
    phone: '',
    companyName: '',
})
let loadmoreStatus = ref('loadmore')
const dataArray: CustomerVisit[] = reactive([])
const loading = ref(false)
const btnLoading = ref(false)
const showSearch = ref(false)
const uToastRef = ref()
const total = ref<Number>(0)

onLoad(async () => {
    uni.$on('refreshCustomerVisit', getList)
    await resetQuery()
})
onUnload(async () => {
    uni.$off('refreshCustomerVisit')
})
onPullDownRefresh(async () => {
    await resetQuery();
    uni.stopPullDownRefresh();
});

const pageChange = async (e: Object) => {
    queryParams.pageNum = e.current
    await getList()
}
const handleQuery = async () => {
    dataArray.length = 0
    queryParams.pageNum = 1
    await getList()
}
const resetQuery = async () => {
    queryParams.name = ''
    queryParams.phone = ''
    queryParams.companyName = ''
    await handleQuery()
}
const getList = async () => {
    const params = Object.assign({}, queryParams)
    loadmoreStatus.value = 'loading'
    loading.value = true
    let projectRes = await uni.$u.http.get('/customer/visit/list', { params })
    loadmoreStatus.value = 'loadmore'
    loading.value = false
    dataArray.length = 0
    dataArray.push(...projectRes.rows)
    total.value = projectRes.total
    if (projectRes.length < queryParams.pageSize) {
        loadmoreStatus.value = 'nomore'
    }
}
</script>
<script lang="ts">
export default {
    options: {
        styleIsolation: "shared"
    },
}
</script>
<style lang="scss" scoped>
::v-deep .uni-table {
    .uni-table-tr {
        .uni-table-th {
            background-color: rgba(248, 248, 249, 1)
        }

        .uni-table-th:last-child,
        .uni-table-td:last-child {
            position: sticky;
            right: 0;
            background-color: rgba(248, 248, 249, 1);
            z-index: 1;
        }

    }
}
</style>