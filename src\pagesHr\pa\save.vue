<template>
    <view class="form-wrapper">

        <uni-section title="申报内容" type="line" />

        <uni-forms :modelValue="form" :rules="rules" ref="formRef" label-width="120">
            <uni-forms-item label="员工姓名" required name="userId">
                <uniStatSelect v-model="form.userId" :localdata="userList" dataValue="userId" dataKey="nickName"
                    :filterable="true" />
            </uni-forms-item>
            <uni-forms-item label="申报类型" required name="type">
                <uni-data-select v-model="form.type" :localdata="typeOptions" />
            </uni-forms-item>
            <uni-forms-item label="分数" required name="score">
                <uni-easyinput trim="both" v-model="form.score" type="number" />
            </uni-forms-item>
            <uni-forms-item label="描述" required name="description">
                <uni-easyinput trim="both" v-model="form.description" />
            </uni-forms-item>
        </uni-forms>

        <uni-section title="流程设置" type="line" />

        <view class="vertical-steps">
            <view v-for="(item, index) in processList" :key="index" class="step-item">
                <view class="step-dot"></view>
                <view class="step-line" v-if="index !== processList.length - 1"></view>
                <view class="step-content">
                    <text class="step-title">{{ item.name }}</text>
                    <view class="step-names">
                        <uni-badge v-for="sub in item.array" :key="sub.userId" class="uni-badge-left-margin" text="x"
                            absolute="rightTop" :offset="[-3, -3]" size="small"
                            @click.stop="delItem(item, sub.userCode)">
                            <view class="name">{{ sub.nickName }}</view>
                        </uni-badge>
                        <text class="ali-icon ali-tianjia" @click="selectItem(item)" />
                    </view>
                </view>
            </view>
        </view>

        <button v-if="!readonly" @click="submit" type="primary" :loading="btnLoading" :disabled="btnLoading">提交</button>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { SysPa } from '@/types/hr/sysPa';
import type { SysHrUser } from '@/types/hr/sysHrUser';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';
import uniStatSelect from '@/components/zxz-uni-data-select.vue'
import { getDictLabel, navTo, getDictData, } from '@/utils/tools';

const form = reactive<SysPa>({
    id: undefined,
    userId: undefined,
    type: '',
    score: undefined,
    description: '',
});
const rules = reactive({
    userId: {
        rules: [
            { required: true, errorMessage: '请选择员工', },
        ]
    },
    type: {
        rules: [
            { required: true, errorMessage: '请选择操作类型', },
        ]
    },
    score: {
        rules: [
            { required: true, errorMessage: '请输入分值', },
        ]
    },
    description: {
        rules: [
            { required: true, errorMessage: '请输入描述', },
        ]
    },
})
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const uToastRef = ref()
const readonly = ref(false)
const currentRow = ref<any | null>()
const userList: SysHrUser[] = reactive([])
const processList: any[] = reactive([])
const typeOptions = [
    { value: '0', text: '加分' },
    { value: '1', text: '减分' },
]
const devDefId = ''
const prodDefId = ''

onLoad(async (o: any) => {
    uni.$on('selectHrUser', selectHrUser)
    const list = await uni.$u.http.get('/hr/user/allIncumbency')
    userList.length = 0
    userList.push(...list)
    const processArray = await uni.$u.http.get('/process/task/userTaskNodes/sys_pa')
    processList.length = 0
    processList.push(...processArray.map((item: any) => {
        return {
            id: item.id,
            name: item.name,
            array: [],
        }
    }))
    if (o.id) {
        await init(o.id)
    }
    if (o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
})
onUnload(async () => {
    uni.$off('selectHrUser')
})

const selectHrUser = (item: SysHrUser) => {
    const array = currentRow.value.array
    if (!(array as SysHrUser[]).map((i: SysHrUser) => i.userCode).includes((item as SysHrUser).userCode)) {
        array.push(item)
    }
}
const delItem = (item: any, userCode: string) => {
    const array = item.array
    const index = array.findIndex((i: SysHrUser) => i.userCode === userCode)
    if (index !== -1) {
        array.splice(index, 1)
    }
}
const selectItem = (e: any) => {
    currentRow.value = e
    navTo('/pages/hr/userSelect')
}
const init = async (id: Number) => {
    loading.value = true
    const res = await uni.$u.http.get('/hr/pa/' + id)
    loading.value = false
    if (res.code === 200) {
        const data = res.data
        Object.assign(form, data)
    }
}
const submit = async () => {
    const validateRes = await formRef.value.validate()
    const subForm = Object.assign({}, form)

    for (const item of processList) {
        if (item.array.length === 0) {
            uToastRef.value.show({
                type: 'error',
                message: `流程设置中 ${item.name} 至少需要选择一人`,
            })
            return
        } else {
            item.userCodes = item.array.map((i: SysHrUser) => i.userCode).join(',')
            item.userNames = item.array.map((i: SysHrUser) => i.nickName).join(',')
        }
    }

    subForm.processList = JSON.stringify(processList)

    let res;
    try {
        btnLoading.value = true
        if (subForm.id != null) {
            res = await uni.$u.http.put('/hr/pa', subForm)
        } else {
            res = await uni.$u.http.post('/hr/pa', subForm)
        }
        btnLoading.value = false
        if (res.code === 200) {
            uToastRef.value.show({
                type: 'success',
                message: '提交成功',
                complete() {
                    uni.$emit('refreshSysPa')
                    uni.navigateBack({
                        delta: 1
                    })
                }
            })
        } else {
            uToastRef.value.show({
                type: 'error',
                message: res.msg,
            })
        }
    } catch (error) {
        btnLoading.value = false
    }
}

</script>
<style lang="scss" scoped>
.vertical-steps {
    padding: 20rpx 0;

    .step-item {
        position: relative;
        padding-left: 80rpx;
        margin-bottom: 40rpx;
        min-height: 120rpx;

        &.last-item {
            margin-bottom: 0;
        }

        .step-dot {
            position: absolute;
            left: 24rpx;
            top: 0;
            width: 24rpx;
            height: 24rpx;
            border-radius: 50%;
            background-color: #ddd;
            border: 6rpx solid #fff;
            z-index: 2;
        }

        .step-line {
            position: absolute;
            left: 35rpx;
            top: 24rpx;
            bottom: -40rpx;
            width: 2rpx;
            background-color: #ddd;
            z-index: 1;
        }

        .step-content {
            .step-title {
                display: block;
                font-size: 32rpx;
                font-weight: bold;
                margin-bottom: 8rpx;
            }

            .step-names {
                display: flex;
                align-items: center;
                justify-content: start;

                .name {
                    height: 28rpx;
                    width: 100rpx;
                    border: 1px solid $uni-border-3;
                    display: flex;
                    justify-content: start;
                    align-items: center;
                    padding: 5rpx;
                    font-size: 24rpx;
                }
            }
        }

        &.active {
            .step-dot {
                background-color: #42b983;
            }

            .step-line {
                background-color: #42b983;
            }

            .step-title {
                color: #42b983;
            }
        }
    }
}
</style>