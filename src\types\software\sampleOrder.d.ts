// 打样单相关类型定义

// 打样单基本信息
export interface SampleOrder {
  id?: number
  sampleOrderCode: string // 打样单编号
  customerId?: number // 客户ID
  customerName?: string // 客户名称
  productName?: string // 产品名称
  completionStatus: number // 完成状态 0-未开始 1-进行中 2-已完成...
  scheduledDate?: string // 排单日期
  endDate?: string // 最晚截至日期
  actualStartTime?: string // 实际开始时间
  actualFinishTime?: string // 实际完成时间
  laboratoryCode?: string // 实验室编码
  userId?: number // 工程师ID
  nickName?: string // 工程师姓名
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  remark?: string // 备注
  itemStatus?: string // 实验室编码是否锁定（0：未锁定可以更改）
}

// 查询参数
export interface SampleOrderQuery {
  pageNum: number
  pageSize: number
  sampleOrderCode?: string // 打样单编号
  completionStatus?: number // 完成状态 - 确保是数字类型或undefined
  customerId?: number // 客户ID
  productName?: string // 产品名称
  laboratoryCode?: string // 实验室编码
  beginDateRange?: string // 开始日期范围
  endDateRange?: string // 结束日期范围
  scheduledDate?: string // 排单日期
  actualStartTime?: string // 实际开始时间
  actualFinishTime?: string // 实际完成时间
  associationStatus?: number // 关联状态 1-已分配
  userId?: number // 工程师ID
  nickName?: string // 工程师姓名
  isOverdue?: number // 是否逾期 1-逾期
}

// 统计数据
export interface DashboardStats {
  total: number // 总任务数
  pending: number // 待处理
  inProgress: number // 进行中
  completed: number // 已完成
  overdue: number // 逾期
}

// 批次管理相关类型定义

// 批次信息
export interface BatchInfo {
  id?: number
  engineerSampleOrderId: number // 工程师打样单ID
  batchIndex: number // 批次序号
  startTime: string // 开始时间
  endTime?: string // 结束时间
  actualManHours?: number // 实际工时
  qualityEvaluation?: string // 质量评价
  remark?: string // 批次备注
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 实验记录
export interface ExperimentRecord {
  id?: number
  batchId: number // 批次ID
  experimentCode: string // 实验编号
  experimentNote?: string // 实验备注
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 批次管理查询参数
export interface BatchQuery {
  pageNum: number
  pageSize: number
  engineerSampleOrderId?: number // 工程师打样单ID
  batchIndex?: number // 批次序号
  startTime?: string // 开始时间
  endTime?: string // 结束时间
}

// 批次统计数据
export interface BatchStats {
  totalBatches: number // 总批次数
  currentBatch: number // 当前批次
  completedBatches: number // 已完成批次数
  totalExperiments: number // 总实验数
  avgManHours: number // 平均工时
}

// 实验室编号选项
export interface ExperimentCodeOption {
  value: string // 实验编号
  label: string // 显示名称
  batchId?: number // 所属批次ID
  createTime?: string // 创建时间
}

// 完成任务表单
export interface FinishTaskForm {
  laboratoryCode: string[] // 选择的实验室编号列表
  remark?: string // 备注
}
