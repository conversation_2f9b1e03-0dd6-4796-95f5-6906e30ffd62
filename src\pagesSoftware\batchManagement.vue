<template>
  <view class="batch-management-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">📋 批次管理</text>
      <text class="page-subtitle">管理打样单的批次进度和实验记录</text>
    </view>

    <!-- 当前批次信息 -->
    <view class="current-batch-section" v-if="currentBatch">
      <view class="section-header">
        <text class="section-title"
          >🔄 当前批次 - 第{{ currentBatch.batchIndex }}批次</text
        >
        <view class="section-actions">
          <u-button
            v-if="canEditBatch"
            type="primary"
            size="mini"
            @click="handleAddExperiment"
            plain
            customStyle="margin-right: 10rpx;"
          >
            添加实验
          </u-button>
          <u-button
            v-if="canEditBatch"
            type="warning"
            size="mini"
            @click="handleFinishCurrentBatch"
            plain
          >
            结束批次
          </u-button>
        </view>
      </view>

      <view class="current-batch-card">
        <view class="batch-info">
          <view class="info-item">
            <text class="info-icon">⏰</text>
            <view class="info-content">
              <text class="info-label">开始时间</text>
              <text class="info-value">{{
                formatDateTime(currentBatch.startTime)
              }}</text>
            </view>
          </view>
          <view class="info-item">
            <text class="info-icon">⏱️</text>
            <view class="info-content">
              <text class="info-label">已用时长</text>
              <text class="info-value">{{
                calculateDuration(currentBatch.startTime)
              }}</text>
            </view>
          </view>
          <view class="info-item">
            <text class="info-icon">📊</text>
            <view class="info-content">
              <text class="info-label">状态</text>
              <uni-tag text="进行中" type="primary" size="mini" />
            </view>
          </view>
        </view>

        <!-- 当前批次实验记录 -->
        <view class="experiments-section">
          <view class="experiments-header">
            <text class="experiments-title">🧪 实验记录</text>
          </view>

          <view
            v-if="currentBatchExperiments.length > 0"
            class="experiments-list"
          >
            <view
              v-for="experiment in currentBatchExperiments"
              :key="experiment.id"
              class="experiment-item"
            >
              <view class="experiment-info">
                <text class="experiment-code">{{
                  experiment.experimentCode
                }}</text>
                <text class="experiment-note">{{
                  experiment.experimentNote || "无备注"
                }}</text>
                <text class="experiment-time">{{
                  formatDateTime(experiment.createTime)
                }}</text>
              </view>
            </view>
          </view>

          <view v-else class="empty-experiments">
            <text class="empty-text">暂无实验记录</text>
            <u-button
              v-if="canEditBatch"
              type="primary"
              size="mini"
              @click="handleAddExperiment"
              customStyle="margin-top: 20rpx;"
            >
              添加实验
            </u-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 开始新批次 -->
    <view class="new-batch-section" v-if="!currentBatch">
      <view class="new-batch-card">
        <view class="new-batch-content">
          <text class="new-batch-icon">🚀</text>
          <text class="new-batch-title">开始新批次</text>
          <text class="new-batch-desc">创建新的批次来管理实验进度</text>
          <u-button
            v-if="canEditBatch"
            type="primary"
            @click="handleStartNewBatch"
            customStyle="margin-top: 30rpx;"
          >
            开始新批次
          </u-button>
          <view v-else class="readonly-tip">
            <text>当前状态不允许开始新批次</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 历史批次列表 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">📚 历史批次</text>
        <view class="section-actions">
          <u-button type="primary" size="mini" @click="handleRefresh" plain>
            刷新
          </u-button>
        </view>
      </view>

      <view v-if="historyBatches.length > 0" class="history-list">
        <view
          v-for="batch in historyBatches"
          :key="batch.id"
          class="history-item"
          @click="handleViewBatchDetail(batch)"
        >
          <view class="batch-header">
            <uni-tag
              :text="`第${batch.batchIndex}批次`"
              type="info"
              size="mini"
            />
            <text class="batch-duration">{{
              formatDuration(batch.startTime, batch.endTime)
            }}</text>
          </view>

          <view class="batch-details">
            <view class="detail-row">
              <text class="detail-label">开始:</text>
              <text class="detail-value">{{
                formatDateTime(batch.startTime)
              }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">结束:</text>
              <text class="detail-value">{{
                batch.endTime ? formatDateTime(batch.endTime) : "未结束"
              }}</text>
            </view>
            <view class="detail-row" v-if="batch.actualManHours">
              <text class="detail-label">工时:</text>
              <text class="detail-value">{{ batch.actualManHours }}小时</text>
            </view>
            <view class="detail-row" v-if="batch.qualityEvaluation">
              <text class="detail-label">质量:</text>
              <text class="detail-value">{{ batch.qualityEvaluation }}</text>
            </view>
          </view>

          <view class="batch-remark" v-if="batch.remark">
            <text class="remark-text">{{ batch.remark }}</text>
          </view>
        </view>
      </view>

      <view v-else class="empty-history">
        <text class="empty-icon">📝</text>
        <text class="empty-text">暂无历史批次记录</text>
      </view>
    </view>

    <!-- 任务操作区域 -->
    <view class="task-action-section" v-if="selectedSampleOrder">
      <view class="section-header">
        <text class="section-title">🎯 任务管理</text>
      </view>

      <!-- 未开始状态 - 开始任务 -->
      <view
        v-if="
          selectedSampleOrder.completionStatus === 0 &&
          hasPerms('software:engineerSampleOrder:startRask')
        "
        class="task-action-card"
      >
        <view class="action-header">
          <text class="action-icon">🚀</text>
          <view class="action-content">
            <text class="action-title">开始任务</text>
            <text class="action-desc"
              >开始处理当前打样单任务，将自动创建第一个批次</text
            >
          </view>
        </view>
        <u-button
          type="success"
          @click="handleStartTask"
          customStyle="width: 100%; margin-top: 20rpx;"
        >
          开始任务
        </u-button>
      </view>

      <!-- 进行中状态 - 任务进行中提示 -->
      <view
        v-if="
          selectedSampleOrder.completionStatus === 1 &&
          hasPerms('software:engineerSampleOrder:endRask')
        "
        class="task-action-card in-progress"
      >
        <view class="action-header">
          <text class="action-icon">⚡</text>
          <view class="action-content">
            <text class="action-title">任务进行中</text>
            <text class="action-desc">请使用上方的批次操作来管理实验进度</text>
          </view>
        </view>
        <view class="task-progress-actions">
          <u-button
            type="primary"
            @click="handleFinishTask"
            customStyle="flex: 1; margin-right: 10rpx;"
          >
            结束任务
          </u-button>
        </view>
      </view>

      <!-- 已完成状态 -->
      <view
        v-if="selectedSampleOrder.completionStatus === 2"
        class="task-action-card completed"
      >
        <view class="action-header">
          <text class="action-icon">🎉</text>
          <view class="action-content">
            <text class="action-title">任务已完成</text>
            <text class="action-desc">当前任务已成功完成</text>
          </view>
        </view>
        <view class="completed-info" v-if="selectedSampleOrder.laboratoryCode">
          <text class="completed-label">实验室编号:</text>
          <text class="completed-value">{{
            selectedSampleOrder.laboratoryCode
          }}</text>
        </view>
        <view class="completed-stats">
          <view class="stat-item">
            <text class="stat-label">总批次数:</text>
            <text class="stat-value">{{ historyBatches.length }}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">总实验数:</text>
            <text class="stat-value">{{ getTotalExperiments() }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加实验弹窗 -->
    <uni-popup ref="addExperimentDialog" type="center" :mask-click="false">
      <view class="add-experiment-dialog">
        <view class="dialog-header">
          <text class="dialog-title">添加实验记录</text>
        </view>
        <view class="dialog-content">
          <view class="form-item">
            <text class="form-label"
              >实验编号 <text class="required">*</text></text
            >
            <u-input
              v-model="experimentForm.experimentCode"
              placeholder="请输入实验编号"
              clearable
            />
          </view>
          <view class="form-item">
            <text class="form-label">实验备注</text>
            <u-textarea
              v-model="experimentForm.experimentNote"
              placeholder="请输入实验备注（可选）"
              :maxlength="200"
              count
            />
          </view>
        </view>
        <view class="dialog-footer">
          <u-button
            @click="handleAddExperimentCancel"
            size="default"
            customStyle="flex: 1; margin-right: 10rpx;"
          >
            取消
          </u-button>
          <u-button
            type="primary"
            @click="handleAddExperimentConfirm"
            size="default"
            customStyle="flex: 1;"
          >
            确认
          </u-button>
        </view>
      </view>
    </uni-popup>

    <!-- 结束批次弹窗 -->
    <uni-popup ref="finishBatchDialog" type="center" :mask-click="false">
      <view class="finish-batch-dialog">
        <view class="dialog-header">
          <text class="dialog-title">结束当前批次</text>
        </view>
        <view class="dialog-content">
          <view class="form-item">
            <text class="form-label">质量评价</text>
            <u-input
              v-model="finishBatchForm.qualityEvaluation"
              placeholder="请输入质量评价（可选）"
              clearable
            />
          </view>
          <view class="form-item">
            <text class="form-label">批次备注</text>
            <u-textarea
              v-model="finishBatchForm.remark"
              placeholder="请输入批次备注（可选）"
              :maxlength="200"
              count
            />
          </view>
        </view>
        <view class="dialog-footer">
          <u-button
            @click="handleFinishBatchCancel"
            size="default"
            customStyle="flex: 1; margin-right: 10rpx;"
          >
            取消
          </u-button>
          <u-button
            type="primary"
            @click="handleFinishBatchConfirm"
            size="default"
            customStyle="flex: 1;"
          >
            确认结束
          </u-button>
        </view>
      </view>
    </uni-popup>

    <!-- 完成任务弹窗 -->
    <uni-popup ref="finishTaskDialog" type="center" :mask-click="false">
      <view class="finish-task-dialog">
        <view class="dialog-header">
          <text class="dialog-title">完成任务</text>
        </view>
        <view class="dialog-content">
          <view class="form-item">
            <text class="form-label"
              >选择实验室编号 <text class="required">*</text></text
            >
            <view class="experiment-code-selector">
              <view
                v-if="experimentCodeOptions.length === 0"
                class="no-options"
              >
                <text class="no-options-text">暂无可选的实验编号</text>
                <text class="no-options-tip">请先添加实验记录</text>
              </view>
              <view v-else class="options-list">
                <view
                  v-for="option in experimentCodeOptions"
                  :key="option.value"
                  class="option-item"
                  :class="{
                    selected: finishTaskForm.laboratoryCode.includes(
                      option.value
                    ),
                  }"
                  @click="toggleExperimentCode(option.value)"
                >
                  <view class="option-content">
                    <text class="option-label">{{ option.label }}</text>
                    <text class="option-time" v-if="option.createTime">
                      {{ formatDateTime(option.createTime) }}
                    </text>
                  </view>
                  <view class="option-checkbox">
                    <uni-icons
                      v-if="
                        finishTaskForm.laboratoryCode.includes(option.value)
                      "
                      type="checkmarkempty"
                      color="#007aff"
                      size="20"
                    />
                    <view v-else class="checkbox-empty"></view>
                  </view>
                </view>
              </view>
              <view
                class="selected-summary"
                v-if="finishTaskForm.laboratoryCode.length > 0"
              >
                <text class="summary-text">
                  已选择 {{ finishTaskForm.laboratoryCode.length }} 个实验编号
                </text>
              </view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">完成备注</text>
            <u-textarea
              v-model="finishTaskForm.remark"
              placeholder="请输入完成备注（可选）"
              :maxlength="200"
              count
            />
          </view>
        </view>
        <view class="dialog-footer">
          <u-button
            @click="handleFinishTaskCancel"
            size="default"
            customStyle="flex: 1; margin-right: 10rpx;"
          >
            取消
          </u-button>
          <u-button
            type="primary"
            @click="handleFinishTaskConfirm"
            size="default"
            customStyle="flex: 1;"
            :disabled="finishTaskForm.laboratoryCode.length === 0"
          >
            确认完成
          </u-button>
        </view>
      </view>
    </uni-popup>

    <!-- Toast 和 Loading 组件 -->
    <up-toast ref="uToastRef" />
    <up-loading-page :loading="loading" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, inject, onMounted, computed } from "vue";
import { onLoad, onPullDownRefresh } from "@dcloudio/uni-app";
import type {
  SampleOrder,
  BatchInfo,
  ExperimentRecord,
  BatchStats,
  ExperimentCodeOption,
  FinishTaskForm,
} from "@/types/software/sampleOrder";

import { hasPerms } from "@/utils/tools";

const dayjs: any = inject("dayjs");
const uToastRef = ref();

// 页面参数
const selectedSampleOrderId = ref<number | null>(null);
const selectedSampleOrder = ref<SampleOrder | null>(null);

// 响应式数据
const loading = ref(false);
const currentBatch = ref<BatchInfo | null>(null);
const historyBatches = ref<BatchInfo[]>([]);
const currentBatchExperiments = ref<ExperimentRecord[]>([]);

// 弹窗引用
const addExperimentDialog = ref();
const finishBatchDialog = ref();
const finishTaskDialog = ref();

// 表单数据
const experimentForm = reactive({
  experimentCode: "",
  experimentNote: "",
});

const finishBatchForm = reactive({
  qualityEvaluation: "",
  remark: "",
});

const finishTaskForm = reactive<FinishTaskForm>({
  laboratoryCode: [],
  remark: "",
});

// 实验编号选项
const experimentCodeOptions = ref<ExperimentCodeOption[]>([]);

// 计算属性
const canEditBatch = computed(() => {
  return selectedSampleOrder.value?.completionStatus == 1; // 只有进行中的任务可以编辑批次
});

// 页面加载时获取参数
onLoad((options) => {
  if (options?.sampleOrderId) {
    selectedSampleOrderId.value = parseInt(options.sampleOrderId);
    loadSampleOrderInfo();
  }
});

// 加载打样单信息
const loadSampleOrderInfo = async () => {
  if (!selectedSampleOrderId.value) return;

  try {
    const res = await uni.$u.http.get(
      `/software/engineerSampleOrder/${selectedSampleOrderId.value}`
    );
    if (res.code === 200) {
      selectedSampleOrder.value = res.data;
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg ? res.msg : "加载打样单信息失败",
      });
    }
  } catch (error) {
    uToastRef.value.show({
      type: "error",
      message: "加载打样单信息异常",
    });
  }
};

// 获取当前批次信息
const getCurrentBatch = async () => {
  if (!selectedSampleOrderId.value) return;

  try {
    const res = await uni.$u.http.get(
      `/software/sampleOrderBatch/currentBatch/${selectedSampleOrderId.value}`
    );
    if (res.code === 200) {
      currentBatch.value = res.data;
      if (currentBatch.value) {
        await getCurrentBatchExperiments();
      }
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg ? res.msg : "获取当前批次失败",
      });
    }
  } catch (error) {
    uToastRef.value.show({
      type: "error",
      message: "获取当前批次异常",
    });
    currentBatch.value = null;
  }
};

// 获取当前批次的实验记录
const getCurrentBatchExperiments = async () => {
  if (!currentBatch.value?.id) return;

  try {
    const res = await uni.$u.http.get(
      `/software/sampleOrderBatch/experiments/${currentBatch.value.id}`
    );
    if (res.code === 200) {
      currentBatchExperiments.value = res.data || [];
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg ? res.msg : "获取实验记录失败",
      });
    }
  } catch (error) {
    uToastRef.value.show({
      type: "error",
      message: "获取实验记录异常",
    });
    currentBatchExperiments.value = [];
  }
};

// 获取历史批次列表
const getHistoryBatches = async () => {
  if (!selectedSampleOrderId.value) return;

  try {
    const res = await uni.$u.http.get(
      `/software/sampleOrderBatch/listByOrderId/${selectedSampleOrderId.value}`
    );
    if (res.code === 200) {
      historyBatches.value = res.data || [];
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg ? res.msg : "获取历史批次失败",
      });
    }
  } catch (error) {
    uToastRef.value.show({
      type: "error",
      message: "获取历史批次异常",
    });
    historyBatches.value = [];
  }
};

// 开启新批次
const handleStartNewBatch = async () => {
  if (!selectedSampleOrderId.value) {
    uToastRef.value.show({
      type: "error",
      message: "打样单信息丢失，请重试",
    });
    return;
  }

  // 如果有当前批次，需要先结束当前批次
  if (currentBatch.value) {
    uni.showModal({
      title: "开启新批次",
      content: "开启新批次前需要先结束当前批次，是否继续？",
      success: async (res) => {
        if (res.confirm) {
          await finishCurrentBatchAndStartNew();
        }
      },
    });
  } else {
    // 直接开启新批次
    await startNewBatch();
  }
};

// 结束当前批次并开启新批次
const finishCurrentBatchAndStartNew = async () => {
  try {
    uni.showLoading({ title: "处理中..." });

    // 1. 结束当前批次
    if (currentBatch.value) {
      const finishRes = await uni.$u.http.post(
        "/software/sampleOrderBatch/finishBatch",
        {
          id: currentBatch.value.id,
          qualityEvaluation: "",
          remark: "自动结束以开启新批次",
        }
      );

      if (finishRes.code !== 200) {
        throw new Error(finishRes.msg || "结束当前批次失败");
      } else {
        uToastRef.value.show({
          type: "error",
          message: finishRes.msg ? finishRes.msg : "结束批次并开启新批次失败",
        });
      }
    }

    // 2. 开启新批次
    await startNewBatch();
  } catch (error) {
    uToastRef.value.show({
      type: "error",
      message: error instanceof Error ? error.message : "操作失败",
    });
  } finally {
    uni.hideLoading();
  }
};

// 开启新批次
const startNewBatch = async () => {
  try {
    uni.showLoading({ title: "创建新批次..." });
    const res = await uni.$u.http.post(
      "/software/sampleOrderBatch/startBatch",
      {},
      {
        params: {
          engineerSampleOrderId: selectedSampleOrderId.value,
          remark: "",
        },
      }
    );

    if (res.code === 200) {
      uToastRef.value.show({
        type: "success",
        message: "新批次已开启",
      });
      await refreshData();
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg ? res.msg : "开启新批次失败",
      });
    }
  } catch (error) {
    uToastRef.value.show({
      type: "error",
      message:
        error instanceof Error
          ? "开启新批次失败:" + error.message
          : "开启新批次失败",
    });
  } finally {
    uni.hideLoading();
  }
};

// 结束当前批次
const handleFinishCurrentBatch = () => {
  if (!currentBatch.value || !canEditBatch.value) {
    uToastRef.value.show({
      type: "error",
      message: "当前状态不允许结束批次",
    });
    return;
  }

  // 重置表单
  finishBatchForm.qualityEvaluation = "";
  finishBatchForm.remark = "";

  // 显示弹窗
  finishBatchDialog.value.open();
};

// 结束批次确认
const handleFinishBatchConfirm = async () => {
  if (!currentBatch.value || !selectedSampleOrderId.value) {
    uToastRef.value.show({
      type: "error",
      message: "批次信息丢失，请重试",
    });
    return;
  }

  try {
    uni.showLoading({ title: "处理中..." });

    const res = await uni.$u.http.post(
      "/software/sampleOrderBatch/finishBatch",
      null,
      {
        params: {
          engineerSampleOrderId: selectedSampleOrderId.value,
          qualityEvaluation: finishBatchForm.qualityEvaluation.trim() || null,
          remark: finishBatchForm.remark.trim() || null,
        },
      }
    );

    if (res.code === 200) {
      finishBatchDialog.value.close();
      uToastRef.value.show({
        type: "success",
        message: "批次已结束",
      });
      await refreshData();
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg || "结束批次失败",
      });
    }
  } catch (error) {
    uToastRef.value.show({
      type: "error",
      message: "结束批次失败",
    });
  } finally {
    uni.hideLoading();
  }
};

// 结束批次取消
const handleFinishBatchCancel = () => {
  finishBatchDialog.value.close();
};

// 添加实验
const handleAddExperiment = () => {
  if (!currentBatch.value || !canEditBatch.value) {
    uToastRef.value.show({
      type: "error",
      message: "当前状态不允许添加实验",
    });
    return;
  }

  // 重置表单
  experimentForm.experimentCode = "";
  experimentForm.experimentNote = "";

  // 显示弹窗
  addExperimentDialog.value.open();
};

// 添加实验确认
const handleAddExperimentConfirm = async () => {
  if (!experimentForm.experimentCode.trim()) {
    uToastRef.value.show({
      type: "error",
      message: "请输入实验编号",
    });
    return;
  }

  if (!currentBatch.value?.id) {
    uToastRef.value.show({
      type: "error",
      message: "批次信息丢失，请重试",
    });
    return;
  }

  try {
    uni.showLoading({ title: "添加中..." });

    const res = await uni.$u.http.post(
      "/software/sampleOrderBatch/addExperiment",
      {
        batchId: currentBatch.value.id,
        experimentCode: experimentForm.experimentCode.trim(),
        experimentNote: experimentForm.experimentNote.trim() || null,
      }
    );

    if (res.code === 200) {
      addExperimentDialog.value.close();
      uToastRef.value.show({
        type: "success",
        message: "实验记录已添加",
      });
      await getCurrentBatchExperiments();
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg || "添加实验记录失败",
      });
    }
  } catch (error) {
    uToastRef.value.show({
      type: "error",
      message: "添加实验记录失败",
    });
  } finally {
    uni.hideLoading();
  }
};

// 添加实验取消
const handleAddExperimentCancel = () => {
  addExperimentDialog.value.close();
};

// 查看批次详情
const handleViewBatchDetail = (batch: BatchInfo) => {
  // 可以跳转到详情页面或显示详情弹窗
  uni.showModal({
    title: `第${batch.batchIndex}批次详情`,
    content: `开始时间: ${formatDateTime(batch.startTime)}\n结束时间: ${
      batch.endTime ? formatDateTime(batch.endTime) : "未结束"
    }\n工时: ${batch.actualManHours || 0}小时\n质量评价: ${
      batch.qualityEvaluation || "无"
    }\n备注: ${batch.remark || "无"}`,
    showCancel: false,
    confirmText: "知道了",
  });
};

// 开始任务
const handleStartTask = async () => {
  if (!selectedSampleOrder.value?.id) {
    uToastRef.value.show({
      type: "error",
      message: "任务信息丢失，请重试",
    });
    return;
  }

  uni.showModal({
    title: "开始任务",
    content: "开始任务后将自动创建第一个批次，确定要开始吗？",
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: "启动中..." });

          // 1. 更新任务状态为进行中
          const updateRes = await uni.$u.http.post(
            "/software/engineerSampleOrder/updateStatus",
            {
              id: selectedSampleOrder.value!.id,
              status: 1,
            }
          );

          if (updateRes.code !== 200) {
            throw new Error(updateRes.msg || "更新任务状态失败");
          }

          uToastRef.value.show({
            type: "success",
            message: "任务已开始，第一个批次已创建",
          });

          // 刷新所有数据
          await refreshData();
        } catch (error) {
          uToastRef.value.show({
            type: "error",
            message: error instanceof Error ? error.message : "开始任务失败",
          });
        } finally {
          uni.hideLoading();
        }
      }
    },
  });
};

// 完成任务
const handleFinishTask = async () => {
  if (!selectedSampleOrder.value?.id) {
    uToastRef.value.show({
      type: "error",
      message: "任务信息丢失，请重试",
    });
    return;
  }

  // 重置表单
  finishTaskForm.laboratoryCode = [];
  finishTaskForm.remark = "";

  // 加载实验编号选项
  await loadExperimentCodeOptions();

  // 显示弹窗
  finishTaskDialog.value.open();
};

// 加载实验编号选项
const loadExperimentCodeOptions = async () => {
  if (!selectedSampleOrderId.value) return;

  try {
    const res = await uni.$u.http.get(
      `/software/sampleOrderBatch/batchExperimentCodeList/${selectedSampleOrderId.value}`
    );

    if (res.code === 200) {
      experimentCodeOptions.value = (res.data || []).map((item: any) => ({
        value: item.experimentCode,
        label: item.experimentCode,
        batchId: item.batchId,
        createTime: item.createTime,
      }));
    } else {
      experimentCodeOptions.value = [];
      uToastRef.value.show({
        type: "warning",
        message: "获取实验编号失败",
      });
    }
  } catch (error) {
    experimentCodeOptions.value = [];
  }
};

// 切换实验编号选择
const toggleExperimentCode = (code: string) => {
  const index = finishTaskForm.laboratoryCode.indexOf(code);
  if (index > -1) {
    finishTaskForm.laboratoryCode.splice(index, 1);
  } else {
    finishTaskForm.laboratoryCode.push(code);
  }
};

// 完成任务确认
const handleFinishTaskConfirm = async () => {
  if (finishTaskForm.laboratoryCode.length === 0) {
    uToastRef.value.show({
      type: "error",
      message: "请选择至少一个实验编号",
    });
    return;
  }

  if (!selectedSampleOrder.value?.id) {
    uToastRef.value.show({
      type: "error",
      message: "任务信息丢失，请重试",
    });
    return;
  }

  try {
    uni.showLoading({ title: "处理中..." });

    const response = await uni.$u.http.post(
      "/software/engineerSampleOrder/updateStatus",
      {
        id: selectedSampleOrder.value.id,
        status: "2",
        laboratoryCode: finishTaskForm.laboratoryCode.join(","),
        remark: finishTaskForm.remark?.trim() || null,
      }
    );

    if (response.code === 200) {
      finishTaskDialog.value.close();
      uToastRef.value.show({
        type: "success",
        message: "任务已完成",
      });
      await loadSampleOrderInfo();
    } else {
      uToastRef.value.show({
        type: "error",
        message: response.msg || "完成任务失败",
      });
    }
  } catch (error) {
    uToastRef.value.show({
      type: "error",
      message: "完成任务失败",
    });
  } finally {
    uni.hideLoading();
  }
};

// 完成任务取消
const handleFinishTaskCancel = () => {
  finishTaskDialog.value.close();
};

// 获取总实验数
const getTotalExperiments = () => {
  return historyBatches.value.reduce((total, batch) => {
    return total + ((batch as any).experimentCount || 0);
  }, 0);
};

// 刷新数据
const handleRefresh = async () => {
  await refreshData();
  uni.showToast({
    title: "刷新成功",
    icon: "success",
    duration: 1500,
  });
};

// 刷新所有数据
const refreshData = async () => {
  loading.value = true;
  await Promise.all([
    getCurrentBatch(),
    getHistoryBatches(),
    loadSampleOrderInfo(),
  ]);
  loading.value = false;
};

// 工具方法
const formatDateTime = (dateStr?: string): string => {
  if (!dateStr) return "";
  return dayjs(dateStr).format("YYYY-MM-DD HH:mm:ss");
};

const formatDate = (dateStr?: string): string => {
  if (!dateStr) return "";
  return dayjs(dateStr).format("YYYY-MM-DD");
};

const calculateDuration = (startTime: string): string => {
  if (!startTime) return "0小时";
  const start = dayjs(startTime);
  const now = dayjs();
  const diffHours = now.diff(start, "hour", true);

  if (diffHours < 1) {
    const diffMinutes = now.diff(start, "minute");
    return `${diffMinutes}分钟`;
  } else if (diffHours < 24) {
    return `${Math.floor(diffHours)}小时${Math.floor(
      (diffHours % 1) * 60
    )}分钟`;
  } else {
    const diffDays = Math.floor(diffHours / 24);
    const remainingHours = Math.floor(diffHours % 24);
    return `${diffDays}天${remainingHours}小时`;
  }
};

const formatDuration = (startTime: string, endTime?: string): string => {
  if (!startTime) return "0小时";
  const start = dayjs(startTime);
  const end = endTime ? dayjs(endTime) : dayjs();
  const diffHours = end.diff(start, "hour", true);

  if (diffHours < 1) {
    const diffMinutes = end.diff(start, "minute");
    return `${diffMinutes}分钟`;
  } else {
    return `${diffHours.toFixed(1)}小时`;
  }
};

// 生命周期
onMounted(async () => {
  if (selectedSampleOrderId.value) {
    await refreshData();
  }
});

// 下拉刷新
onPullDownRefresh(() => {
  handleRefresh().finally(() => {
    uni.stopPullDownRefresh();
  });
});
</script>

<style lang="scss" scoped>
.batch-management-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 页面标题样式
.page-header {
  text-align: center;
  margin-bottom: 30rpx;

  .page-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #303133;
    margin-bottom: 10rpx;
  }

  .page-subtitle {
    display: block;
    font-size: 24rpx;
    color: #909399;
  }
}

// 区域标题样式
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #303133;
  }

  .section-actions {
    display: flex;
    gap: 10rpx;
  }
}

// 当前批次样式
.current-batch-section {
  margin-bottom: 30rpx;
}

.current-batch-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);

  .batch-info {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    margin-bottom: 30rpx;

    .info-item {
      display: flex;
      align-items: center;
      padding: 16rpx;
      background-color: #fafafa;
      border-radius: 12rpx;
      border-left: 6rpx solid #409eff;

      .info-icon {
        font-size: 32rpx;
        margin-right: 16rpx;
        flex-shrink: 0;
      }

      .info-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8rpx;

        .info-label {
          font-size: 24rpx;
          color: #909399;
          font-weight: 500;
        }

        .info-value {
          font-size: 28rpx;
          color: #303133;
          font-weight: 600;
        }
      }
    }
  }
}

// 实验记录样式
.experiments-section {
  border-top: 2rpx solid #f0f0f0;
  padding-top: 20rpx;

  .experiments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .experiments-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #303133;
    }
  }

  .experiments-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }

  .experiment-item {
    background-color: #f8f9fa;
    border-radius: 12rpx;
    padding: 20rpx;
    border-left: 4rpx solid #67c23a;

    .experiment-info {
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .experiment-code {
        font-size: 28rpx;
        font-weight: 600;
        color: #303133;
      }

      .experiment-note {
        font-size: 24rpx;
        color: #606266;
      }

      .experiment-time {
        font-size: 22rpx;
        color: #909399;
      }
    }
  }

  .empty-experiments {
    text-align: center;
    padding: 40rpx;
    color: #909399;

    .empty-text {
      display: block;
      font-size: 28rpx;
      margin-bottom: 20rpx;
    }
  }
}

// 开始新批次样式
.new-batch-section {
  margin-bottom: 30rpx;
}

.new-batch-card {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);

  .new-batch-content {
    text-align: center;

    .new-batch-icon {
      display: block;
      font-size: 80rpx;
      margin-bottom: 20rpx;
    }

    .new-batch-title {
      display: block;
      font-size: 32rpx;
      font-weight: 600;
      color: #303133;
      margin-bottom: 10rpx;
    }

    .new-batch-desc {
      display: block;
      font-size: 24rpx;
      color: #909399;
      margin-bottom: 30rpx;
    }

    .readonly-tip {
      color: #909399;
      font-size: 28rpx;
      margin-top: 30rpx;
    }
  }
}

// 历史批次样式
.history-section {
  margin-bottom: 30rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  }

  .batch-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .batch-duration {
      font-size: 24rpx;
      color: #67c23a;
      font-weight: 600;
    }
  }

  .batch-details {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    margin-bottom: 16rpx;

    .detail-row {
      display: flex;
      align-items: center;

      .detail-label {
        font-size: 24rpx;
        color: #909399;
        width: 80rpx;
        flex-shrink: 0;
      }

      .detail-value {
        font-size: 24rpx;
        color: #303133;
        flex: 1;
      }
    }
  }

  .batch-remark {
    padding: 12rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
    border-left: 4rpx solid #e6a23c;

    .remark-text {
      font-size: 24rpx;
      color: #606266;
      line-height: 1.4;
    }
  }
}

.empty-history {
  text-align: center;
  padding: 80rpx 40rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);

  .empty-icon {
    display: block;
    font-size: 80rpx;
    margin-bottom: 20rpx;
    color: #c0c4cc;
  }

  .empty-text {
    font-size: 28rpx;
    color: #909399;
  }
}

// 任务操作区域
.task-action-section {
  margin: 30rpx 0;

  .task-action-card {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin: 20rpx 0;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    border: 2rpx solid #f0f0f0;

    &.completed {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-color: #28a745;
    }

    &.in-progress {
      background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
      border-color: #ffc107;
    }

    .action-header {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20rpx;

      .action-icon {
        font-size: 40rpx;
        margin-right: 20rpx;
        line-height: 1;
      }

      .action-content {
        flex: 1;

        .action-title {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 8rpx;
        }

        .action-desc {
          display: block;
          font-size: 26rpx;
          color: #666666;
          line-height: 1.4;
        }
      }
    }

    .task-progress-actions {
      display: flex;
      gap: 10rpx;
      margin-top: 20rpx;
    }

    .completed-info {
      margin-top: 20rpx;
      padding: 20rpx;
      background: rgba(40, 167, 69, 0.1);
      border-radius: 12rpx;
      border-left: 6rpx solid #28a745;

      .completed-label {
        font-size: 26rpx;
        color: #666666;
        margin-right: 10rpx;
      }

      .completed-value {
        font-size: 28rpx;
        color: #28a745;
        font-weight: 600;
      }
    }

    .completed-stats {
      margin-top: 20rpx;
      display: flex;
      gap: 30rpx;
      padding: 20rpx;
      background: rgba(40, 167, 69, 0.05);
      border-radius: 12rpx;

      .stat-item {
        flex: 1;
        text-align: center;

        .stat-label {
          display: block;
          font-size: 24rpx;
          color: #666666;
          margin-bottom: 8rpx;
        }

        .stat-value {
          display: block;
          font-size: 32rpx;
          color: #28a745;
          font-weight: 600;
        }
      }
    }
  }
}

// 弹窗样式
.add-experiment-dialog,
.finish-batch-dialog,
.finish-task-dialog {
  background: white;
  border-radius: 16rpx;
  width: 600rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);

  .dialog-header {
    padding: 32rpx 32rpx 0;
    text-align: center;

    .dialog-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #303133;
    }
  }

  .dialog-content {
    padding: 32rpx;

    .form-item {
      margin-bottom: 24rpx;

      .form-label {
        display: block;
        font-size: 28rpx;
        color: #303133;
        margin-bottom: 12rpx;
        font-weight: 500;

        .required {
          color: #f56c6c;
          margin-left: 4rpx;
        }
      }
    }
  }

  .dialog-footer {
    padding: 0 32rpx 32rpx;
    display: flex;
    gap: 20rpx;
  }
}

// 实验编号选择器样式
.experiment-code-selector {
  .no-options {
    text-align: center;
    padding: 60rpx 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    border: 2rpx dashed #dee2e6;

    .no-options-text {
      display: block;
      font-size: 28rpx;
      color: #6c757d;
      margin-bottom: 8rpx;
    }

    .no-options-tip {
      display: block;
      font-size: 24rpx;
      color: #adb5bd;
    }
  }

  .options-list {
    max-height: 400rpx;
    overflow-y: auto;
    border: 2rpx solid #e9ecef;
    border-radius: 12rpx;

    .option-item {
      display: flex;
      align-items: center;
      padding: 20rpx;
      border-bottom: 1rpx solid #f1f3f4;
      cursor: pointer;
      transition: all 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: #f8f9fa;
      }

      &.selected {
        background: #e3f2fd;
        border-color: #2196f3;
      }

      .option-content {
        flex: 1;

        .option-label {
          display: block;
          font-size: 28rpx;
          color: #333333;
          font-weight: 500;
          margin-bottom: 4rpx;
        }

        .option-time {
          display: block;
          font-size: 24rpx;
          color: #999999;
        }
      }

      .option-checkbox {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .checkbox-empty {
          width: 20rpx;
          height: 20rpx;
          border: 2rpx solid #ddd;
          border-radius: 4rpx;
        }
      }
    }
  }

  .selected-summary {
    margin-top: 16rpx;
    padding: 16rpx;
    background: #e8f5e8;
    border-radius: 8rpx;
    text-align: center;

    .summary-text {
      font-size: 26rpx;
      color: #28a745;
      font-weight: 500;
    }
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .batch-info {
    .info-item {
      flex-direction: column;
      align-items: flex-start;

      .info-icon {
        margin-bottom: 8rpx;
        margin-right: 0;
      }
    }
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
}
</style>
