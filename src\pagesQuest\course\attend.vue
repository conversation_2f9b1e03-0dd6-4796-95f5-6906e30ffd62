<template>
    <view class="page-wrapper">
        <uni-title type="h4" title="培训标题" />
        <view>
            <text>
                {{ course.title }}
            </text>
        </view>
        <uni-title type="h4" title="培训类型" />
        <view>
            <text>
                {{ course.type === '1' ? '线上' : '线下' }}
            </text>
        </view>
        <uni-title type="h4" title="培训时间" />
        <view>
            <text>
                {{ course.startTime }}
            </text>
        </view>
        <uni-title type="h4" title="培训讲师" />
        <view>
            <text>
                {{ course.teacher }}
            </text>
        </view>
        <uni-title type="h4" title="培训地址" />
        <view>
            <text>
                {{ course.address }}
            </text>
        </view>
    </view>
    <view class="btn-wrapper">
        <button :loading="btnLoading" :disabled="btnLoading" class="success" size="mini" @click="reply('1')">
            参加
        </button>
        <button :loading="btnLoading" :disabled="btnLoading" size="mini" class="error" @click="reply('0')">
            不参加
        </button>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { Course } from '@/types/system/course';
import type { CourseInvite } from '@/types/system/courseInvite';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';

const btnLoading = ref(false)
const loading = ref(false)
const uToastRef = ref()
const form = reactive<CourseInvite>({
    id: undefined,
    courseId: undefined,
    userId: undefined,
    attendFlag: '',
});
const course = reactive<Course>({
    id: undefined,
    type: '',
    title: '',
    personNums: undefined,
    startTime: '',
    teacher: '',
    address: '',
});

onLoad(async (o: any) => {
    await init(o.courseId)
})

const init = async (courseId: number) => {
    loading.value = true
    const res = await uni.$u.http.get('/system/courseInvite/getByCourseId/' + courseId)
    loading.value = false
    if (res.code === 200) {
        const data = res.data
        Object.assign(form, data)
    }
    const courseRes = await uni.$u.http.get('/system/course/' + courseId)
    if (courseRes.code === 200) {
        const data = courseRes.data
        Object.assign(course, data)
    }
}
const reply = async (attendFlag: string) => {
    const params = {
        id: form.id,
        attendFlag,
    }
    try {
        btnLoading.value = true
        const res = await uni.$u.http.put('/system/courseInvite', params)
        btnLoading.value = false
        if (res.code === 200) {
            uToastRef.value.show({
                type: 'success',
                message: '提交成功',
                complete() {
                    uni.reLaunch({
                        url: '/pages/index/index'
                    })
                }
            })
        } else {
            uToastRef.value.show({
                type: 'error',
                message: res.msg,
            })
        }
    } catch (error) {
        btnLoading.value = false
    }
}
</script>
<style lang="scss" scoped>
.btn-wrapper {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.success {
    color: #fff;
    background-color: $uni-success;
}

.error {
    color: #fff;
    background-color: $uni-error;
}
</style>