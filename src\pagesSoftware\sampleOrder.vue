<template>
  <view class="sample-order-container">
    <!-- 统计概览  -->
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stats-card total" @click="handleStatusFilter(null)">
          <view class="stats-number">{{ dashboardStats.total || 0 }}</view>
          <view class="stats-title">总任务</view>
        </view>
        <view class="stats-card in-progress" @click="handleStatusFilter(1)">
          <view class="stats-number">{{ dashboardStats.inProgress || 0 }}</view>
          <view class="stats-title">进行中</view>
        </view>
        <view class="stats-card completed" @click="handleStatusFilter(2)">
          <view class="stats-number">{{ dashboardStats.completed || 0 }}</view>
          <view class="stats-title">已完成</view>
        </view>
        <view class="stats-card overdue" @click="handleOverdueFilter()">
          <view class="stats-number">{{ dashboardStats.overdue || 0 }}</view>
          <view class="stats-title">已逾期</view>
        </view>
      </view>
    </view>

    <!-- 筛选条件 -->
    <view class="filter-section" v-show="showSearch">
      <view class="filter-item">
        <u-input
          v-model="queryParams.sampleOrderCode"
          placeholder="请输入打样单编号"
          clearable
          prefixIcon="search"
        />
      </view>

      <view class="filter-item">
        <u-input
          v-model="queryParams.nickName"
          placeholder="请输入工程师名称"
          clearable
        />
      </view>

      <!-- <view class="filter-item">
        <u-input
          v-model="queryParams.laboratoryCode"
          placeholder="请输入实验室编码"
          clearable
        />
      </view> -->

      <view class="filter-item">
        <u-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
        />
      </view>

      <view class="filter-item">
        <uni-forms-item label="完成情况">
          <uni-data-select
            v-model="queryParams.completionStatus"
            :localdata="statusOptions"
          />
        </uni-forms-item>
      </view>

      <view class="filter-buttons">
        <u-button
          type="primary"
          @click="handleQuery"
          size="default"
          customStyle="flex: 1; margin-right: 10rpx;"
        >
          搜索
        </u-button>
        <u-button @click="resetQuery" size="default" customStyle="flex: 1;">
          重置
        </u-button>
      </view>
    </view>

    <!-- 工具栏 -->
    <view class="toolbar">
      <view class="toolbar-left">
        <u-button
          :type="showSearch ? 'warning' : 'primary'"
          size="mini"
          @click="showSearch = !showSearch"
          plain
        >
          {{ showSearch ? "隐藏筛选" : "显示筛选" }}
        </u-button>
        <u-button
          type="success"
          size="mini"
          @click="handleRefresh"
          plain
          customStyle="margin-left: 20rpx;"
        >
          刷新
        </u-button>
      </view>
    </view>

    <!-- 任务列表 -->
    <view class="task-list">
      <!-- 数据加载状态提示 -->
      <view v-if="loading" class="loading-tip">
        <text>正在加载数据...</text>
      </view>

      <!-- 数据为空时的提示 -->
      <view v-else-if="sampleOrderList.length === 0" class="empty-tip">
        <text>暂无打样任务数据</text>
        <u-button
          type="primary"
          size="mini"
          @click="handleRefresh"
          customStyle="margin-top: 20rpx;"
        >
          重新加载
        </u-button>
      </view>

      <!-- 数据表格 -->
      <uni-table v-else border stripe emptyText="暂无任务数据">
        <uni-tr>
          <uni-th width="120" align="center">打样单编号</uni-th>
          <uni-th width="100" align="center">客户名称</uni-th>
          <uni-th width="100" align="center">产品名称</uni-th>
          <uni-th width="100" align="center">排单日期</uni-th>
          <uni-th width="80" align="center">状态</uni-th>
          <uni-th width="80" align="center">工程师</uni-th>
          <uni-th width="120" align="center">操作</uni-th>
        </uni-tr>
        <uni-tr v-for="item in sampleOrderList" :key="item.id || Math.random()">
          <uni-td>
            <text class="code-text" @click="handleTaskDetail(item)">
              {{ item.sampleOrderCode || "-" }}
            </text>
          </uni-td>
          <uni-td>{{ item.customerName || "-" }}</uni-td>
          <uni-td>{{ item.productName || "-" }}</uni-td>
          <uni-td>{{ formatDate(item.scheduledDate) }}</uni-td>
          <uni-td>
            <uni-tag
              :text="getDictLabel('DYD_GCSZT', item.completionStatus + '')"
              :type="getStatusType(item.completionStatus)"
              size="mini"
            />
          </uni-td>
          <uni-td>{{ item.nickName || "-" }}</uni-td>
          <uni-td>
            <view class="action-buttons">
              <!-- 更换工程师或更改日期 -->
              <u-button
                v-if="
                  item.completionStatus === 0 &&
                  hasPerms('software:engineerSampleOrder:changeEngineer')
                "
                type="warning"
                size="mini"
                @click="handleChangeEngineer(item)"
                customStyle="margin-right: 10rpx;"
              >
                更换工程师
              </u-button>
              <!-- 未开始状态 -->
              <!-- <u-button
                v-if="
                  item.completionStatus === 0 &&
                  hasPerms('software:engineerSampleOrder:startRask')
                "
                type="primary"
                size="mini"
                @click="handleStart(item)"
              >
                开始
              </u-button> -->

              <!-- 进行中状态 -->
              <!-- <u-button
                v-if="
                  item.completionStatus === 1 &&
                  hasPerms('software:engineerSampleOrder:endRask')
                "
                type="success"
                size="mini"
                @click="handleFinish(item)"
              >
                完成
              </u-button> -->

              <!-- 驳回按钮 - 进行中状态可以驳回 -->
              <u-button
                v-if="
                  item.completionStatus === 0 &&
                  hasPerms('software:engineerSampleOrder:rejectRask')
                "
                type="error"
                size="mini"
                @click="handleReject(item)"
                customStyle="margin-right: 10rpx;"
              >
                驳回
              </u-button>

              <!-- 已完成状态 - 修改实验室编码和备注 -->
              <u-button
                v-if="
                  item.completionStatus === 2 &&
                  (item.itemStatus == null || item.itemStatus == '0') &&
                  hasPerms('software:engineerSampleOrder:editSampleOrderCode')
                "
                type="warning"
                size="mini"
                @click="handleUpdateLaboratoryCode(item)"
                customStyle="margin-right: 10rpx;"
              >
                修改
              </u-button>

              <!-- 逾期操作按钮 - 只有逾期的任务才显示 -->
              <u-button
                v-if="
                  isOverdueTask(item) &&
                  hasPerms('software:engineerSampleOrder:overdueOperation')
                "
                type="error"
                size="mini"
                @click="handleOverdueOperation(item)"
                customStyle="margin-right: 10rpx;"
              >
                逾期操作
              </u-button>

              <!-- 批次管理按钮 - 进行中状态可以管理批次 -->
              <u-button
                v-if="
                  (item.completionStatus === 1 ||
                    item.completionStatus === 0 ||
                    item.completionStatus === 2) &&
                  hasPerms('software:engineerSampleOrder:batchManagement')
                "
                type="primary"
                size="mini"
                @click="handleBatchManagement(item)"
                customStyle="margin-right: 10rpx;"
              >
                批次管理
              </u-button>

              <!-- 查看详情 -->
              <u-button
                class="detail-btn"
                type="info"
                size="mini"
                @click="handleTaskDetail(item)"
                >详情</u-button
              >
            </view>
          </uni-td>
        </uni-tr>
      </uni-table>
    </view>

    <!-- 分页 -->
    <uni-pagination
      :current="queryParams.pageNum"
      :total="total"
      :page-size="queryParams.pageSize"
      @change="pageChange"
    />

    <!-- 完成任务弹窗 -->
    <uni-popup ref="finishDialog" type="center" :mask-click="false">
      <view class="finish-dialog">
        <view class="dialog-header">
          <text class="dialog-title">完成任务</text>
        </view>
        <view class="dialog-content">
          <view class="finish-form">
            <view class="form-item">
              <text class="form-label"
                >实验室编号 <text class="required">*</text></text
              >
              <u-input
                v-model="finishForm.laboratoryCode"
                placeholder="请输入实验室编号"
                clearable
              />
            </view>
            <view class="form-item">
              <text class="form-label">备注</text>
              <u-textarea
                v-model="finishForm.remark"
                placeholder="请输入备注信息（可选）"
                :maxlength="200"
                count
              />
            </view>
          </view>
        </view>
        <view class="dialog-footer">
          <u-button
            @click="handleFinishCancel"
            size="default"
            customStyle="flex: 1; margin-right: 10rpx;"
          >
            取消
          </u-button>
          <u-button
            type="primary"
            @click="handleFinishConfirm"
            size="default"
            customStyle="flex: 1;"
          >
            确认
          </u-button>
        </view>
      </view>
    </uni-popup>

    <!-- 更新实验室编码弹窗 -->
    <uni-popup
      ref="updateLaboratoryCodeDialog"
      type="center"
      :mask-click="false"
    >
      <view class="finish-dialog">
        <view class="dialog-header">
          <text class="dialog-title">修改实验室编码和备注</text>
        </view>
        <view class="dialog-content">
          <view class="finish-form">
            <view class="form-item">
              <text class="form-label"
                >实验室编号 <text class="required">*</text></text
              >
              <u-input
                v-model="updateLaboratoryCodeForm.laboratoryCode"
                placeholder="请输入实验室编号"
                clearable
              />
            </view>
            <view class="form-item">
              <text class="form-label">备注</text>
              <u-textarea
                v-model="updateLaboratoryCodeForm.remark"
                placeholder="请输入备注信息（可选）"
                :maxlength="200"
                count
              />
            </view>
          </view>
        </view>
        <view class="dialog-footer">
          <u-button
            @click="handleUpdateLaboratoryCodeCancel"
            size="default"
            customStyle="flex: 1; margin-right: 10rpx;"
          >
            取消
          </u-button>
          <u-button
            type="primary"
            @click="handleUpdateLaboratoryCodeConfirm"
            size="default"
            customStyle="flex: 1;"
          >
            保存
          </u-button>
        </view>
      </view>
    </uni-popup>

    <!-- 详情弹窗 -->
    <uni-popup ref="detailDialog" type="center" :mask-click="true">
      <view class="detail-dialog">
        <view class="dialog-header">
          <text class="dialog-title">📋 打样单详情</text>
          <text class="close-btn" @click="handleDetailClose">✕</text>
        </view>
        <view class="dialog-content">
          <view class="detail-info">
            <view class="info-item">
              <text class="info-icon">📋</text>
              <view class="info-content">
                <text class="info-label">打样单编号</text>
                <text class="info-value">{{
                  currentDetail.sampleOrderCode || "未知"
                }}</text>
              </view>
            </view>
            <view class="info-item">
              <text class="info-icon">👤</text>
              <view class="info-content">
                <text class="info-label">客户名称</text>
                <text class="info-value">{{
                  currentDetail.customerName || "未指定"
                }}</text>
              </view>
            </view>
            <view class="info-item">
              <text class="info-icon">📦</text>
              <view class="info-content">
                <text class="info-label">产品名称</text>
                <text class="info-value">{{
                  currentDetail.productName || "未指定"
                }}</text>
              </view>
            </view>
            <view class="info-item">
              <text class="info-icon">👨‍💼</text>
              <view class="info-content">
                <text class="info-label">负责工程师</text>
                <text class="info-value">{{
                  currentDetail.nickName || "未分配"
                }}</text>
              </view>
            </view>
            <view class="info-item">
              <text class="info-icon">📊</text>
              <view class="info-content">
                <text class="info-label">任务状态</text>
                <uni-tag
                  :text="
                    getDictLabel(
                      'DYD_GCSZT',
                      currentDetail.completionStatus + ''
                    )
                  "
                  :type="getStatusType(currentDetail.completionStatus)"
                  size="mini"
                />
              </view>
            </view>
            <view class="info-item" v-if="currentDetail.scheduledDate">
              <text class="info-icon">📅</text>
              <view class="info-content">
                <text class="info-label">排单日期</text>
                <text class="info-value">{{
                  formatDate(currentDetail.scheduledDate)
                }}</text>
              </view>
            </view>
            <view class="info-item" v-if="currentDetail.actualStartTime">
              <text class="info-icon">🚀</text>
              <view class="info-content">
                <text class="info-label">开始时间</text>
                <text class="info-value">{{
                  formatDate(currentDetail.actualStartTime)
                }}</text>
              </view>
            </view>
            <view class="info-item" v-if="currentDetail.actualFinishTime">
              <text class="info-icon">✅</text>
              <view class="info-content">
                <text class="info-label">完成时间</text>
                <text class="info-value">{{
                  formatDate(currentDetail.actualFinishTime)
                }}</text>
              </view>
            </view>
            <view class="info-item" v-if="currentDetail.laboratoryCode">
              <text class="info-icon">🧪</text>
              <view class="info-content">
                <text class="info-label">实验室编码</text>
                <text class="info-value">{{
                  currentDetail.laboratoryCode
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 更换工程师对话框 -->
    <ChangeEngineerDialog
      ref="changeEngineerDialogRef"
      @confirm="handleChangeEngineerConfirm"
      @close="handleChangeEngineerClose"
    />

    <!-- 驳回对话框 -->
    <RejectDialog
      ref="rejectDialogRef"
      @confirm="handleRejectConfirm"
      @close="handleRejectClose"
    />

    <!-- 逾期操作弹窗 -->
    <uni-popup ref="overdueDialog" type="center" :mask-click="false">
      <view class="overdue-dialog">
        <view class="dialog-header">
          <text class="dialog-title">逾期操作</text>
        </view>
        <view class="dialog-content">
          <view class="overdue-form">
            <view class="form-item">
              <text class="form-label">打样单编号</text>
              <text class="form-value">{{
                currentOverdueRow?.sampleOrderCode || "-"
              }}</text>
            </view>

            <!-- 预计出样时间 -->
            <view class="form-item">
              <text class="form-label">预计出样时间</text>
              <!-- 日期时间选择器 -->
              <uni-datetime-picker
                type="datetime"
                :start="dayjs().format('YYYY-MM-DD')"
                :clear-icon="false"
                v-model="overdueForm.expectedSampleTime"
              />
            </view>

            <view class="form-item">
              <text class="form-label">未出样原因</text>
              <u-textarea
                v-model="overdueForm.reasonForNoSample"
                placeholder="请输入未出样原因（可选）"
                :maxlength="200"
                count
              />
            </view>
            <view class="form-item">
              <text class="form-label">解决方案</text>
              <u-textarea
                v-model="overdueForm.solution"
                placeholder="请输入解决方案（可选）"
                :maxlength="200"
                count
              />
            </view>
          </view>
        </view>
        <view class="dialog-footer">
          <u-button
            @click="handleOverdueCancel"
            size="default"
            customStyle="flex: 1; margin-right: 10rpx;"
          >
            取消
          </u-button>
          <u-button
            type="primary"
            @click="handleOverdueConfirm"
            size="default"
            customStyle="flex: 1;"
          >
            确定
          </u-button>
        </view>
      </view>
    </uni-popup>

    <!-- Toast 和 Loading 组件 -->
    <up-toast ref="uToastRef" />
    <up-loading-page :loading="loading" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, inject, onMounted } from "vue";
import { onPullDownRefresh } from "@dcloudio/uni-app";
import type {
  SampleOrder,
  SampleOrderQuery,
  DashboardStats,
} from "@/types/software/sampleOrder";

import { getDictLabel, getDictData } from "@/utils/tools";
import ChangeEngineerDialog from "./components/ChangeEngineerDialog.vue";
import RejectDialog from "./components/RejectDialog.vue";
import { useAuthStore } from "@/state/modules/user";
import { hasPerms } from "@/utils/tools";

const authStore = useAuthStore();

const dayjs: any = inject("dayjs");
const uToastRef = ref();
// 响应式数据
const loading = ref(false);
const showSearch = ref(false);
const sampleOrderList = ref<SampleOrder[]>([]);
const total = ref(0);
const dashboardStats = ref<DashboardStats>({
  total: 0,
  pending: 0,
  inProgress: 0,
  completed: 0,
  overdue: 0,
});

// 查询参数
const queryParams = reactive<SampleOrderQuery>({
  pageNum: 1,
  pageSize: 10,
  sampleOrderCode: "",
  completionStatus: undefined,
  customerId: undefined,
  productName: "",
  nickName: authStore.user.nickName,
  laboratoryCode: "",
  isOverdue: undefined,
  associationStatus: 1, // 添加关联状态，只查询已分配的任务
});

// 日期范围
const dateRange = ref<string[]>([]);
const dateRangeText = ref("");

const currentTaskItem = ref<SampleOrder | null>(null);

// 打样单状态字典
const statusOptions = getDictData("DYD_GCSZT");

// 完成任务弹窗相关数据
const finishDialog = ref();
const finishForm = reactive({
  laboratoryCode: "",
  remark: "",
});

// 更新实验室编码弹窗相关数据
const updateLaboratoryCodeDialog = ref();
const currentUpdateLaboratoryCodeRow = ref<SampleOrder | null>(null);
const updateLaboratoryCodeForm = reactive({
  laboratoryCode: "",
  remark: "",
});

// 详情弹窗相关数据
const detailDialog = ref();
const currentDetail = ref<SampleOrder>({} as SampleOrder);

// 更换工程师对话框相关数据
const changeEngineerDialogRef = ref();

// 驳回对话框相关数据
const rejectDialogRef = ref();

// 逾期操作弹窗相关数据
const overdueDialog = ref();
const currentOverdueRow = ref<SampleOrder | null>(null);
const overdueForm = reactive({
  expectedSampleTime: "",
  reasonForNoSample: "",
  solution: "",
});

// 获取打样单列表
const getList = async () => {
  loading.value = true;
  try {
    const params: any = { ...queryParams };

    // 添加日期范围参数
    // if (dateRange.value.length === 2) {
    //   params.beginDateRange = dateRange.value[0]
    //   params.endDateRange = dateRange.value[1]
    // }

    const res = await uni.$u.http.get("/software/engineerSampleOrder/list", {
      params,
    });
    if (res.code === 200) {
      sampleOrderList.value = res.rows;
      total.value = res.total;
      if (sampleOrderList.value.length === 0) {
        uni.showToast({
          title: "暂无数据",
          icon: "none",
          duration: 2000,
        });
      }
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg ? res.msg : "获取列表失败",
      });
    }
  } catch (error) {
    sampleOrderList.value = [];
    total.value = 0;
    uToastRef.value.show({
      type: "error",
      message: error instanceof Error ? error.message : "获取列表失败",
    });
  } finally {
    loading.value = false;
    // 停止下拉刷新
    uni.stopPullDownRefresh();
  }
};

// 仪表数据
const loadDashboardStats = async () => {
  try {
    const params: any = { ...queryParams };

    // 添加日期范围参数
    // if (dateRange.value.length === 2) {
    //   params.beginDateRange = dateRange.value[0]
    //   params.endDateRange = dateRange.value[1]
    // }

    const res = await uni.$u.http.get(
      "/software/engineerSampleOrder/dashboardStats",
      { params }
    );

    // 处理统计数据响应
    if (res.code === 200) {
      dashboardStats.value = res.data;
    } else {
      dashboardStats.value = {
        total: 0,
        pending: 0,
        inProgress: 0,
        completed: 0,
        overdue: 0,
      };
    }
  } catch (error) {
    // 设置默认值
    dashboardStats.value = {
      total: 0,
      pending: 0,
      inProgress: 0,
      completed: 0,
      overdue: 0,
    };
  }
};

// 事件处理
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
  loadDashboardStats();
};

const resetQuery = () => {
  // 重置查询参数，保持关联状态
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  queryParams.sampleOrderCode = "";
  queryParams.completionStatus = undefined;
  queryParams.customerId = undefined;
  queryParams.productName = "";
  queryParams.nickName = "";
  queryParams.laboratoryCode = "";
  queryParams.associationStatus = 1; // 保持只查询已分配的任务

  // 重置UI状态
  dateRange.value = [];
  dateRangeText.value = "";

  // 重新查询
  handleQuery();

  // 提示用户
  uni.showToast({
    title: "筛选条件已重置",
    icon: "success",
    duration: 1500,
  });
};

const handleRefresh = async () => {
  try {
    await Promise.all([getList(), loadDashboardStats()]);
    uni.showToast({
      title: "刷新成功",
      icon: "success",
      duration: 1500,
    });
  } catch (error) {
    uni.showToast({
      title: "刷新失败",
      icon: "error",
      duration: 2000,
    });
  }
};

const handleStatusFilter = (status: number | null) => {
  // 确保传递正确的类型给后端
  if (status === null) {
    queryParams.completionStatus = undefined;
  } else {
    queryParams.completionStatus = status;
  }
  queryParams.pageNum = 1; // 重置到第一页
  handleQuery();
};

/** 处理逾期任务过滤 */
const handleOverdueFilter = () => {
  // 清除状态
  queryParams.completionStatus = undefined;

  // 设置是否查询逾期
  queryParams.isOverdue = queryParams.isOverdue === 1 ? 0 : 1;

  // 重置到第一页
  queryParams.pageNum = 1;
  handleQuery();
  if (queryParams.isOverdue === 1) {
    uni.showToast({
      title: "已筛选显示逾期任务",
      icon: "none",
      duration: 1500,
    });
  } else {
    uni.showToast({
      title: "已清除逾期任务筛选",
      icon: "none",
      duration: 1500,
    });
  }
};

const pageChange = (page: number) => {
  // 更新查询参数中的页码
  queryParams.pageNum = page.current;
  getList();
};

// 任务操作
const handleTaskDetail = (item: SampleOrder) => {
  // 设置当前详情数据
  currentDetail.value = item;
  // 显示详情弹窗
  detailDialog.value.open();
};

// 关闭详情弹窗
const handleDetailClose = () => {
  detailDialog.value.close();
};

const handleStart = async (item: SampleOrder) => {
  try {
    uni.showLoading({ title: "处理中..." });

    const res = await uni.$u.http.post(
      "/software/engineerSampleOrder/updateStatus",
      {
        id: item.id,
        status: 1,
      }
    );

    if (res.code === 200) {
      uToastRef.value.show({
        type: "success",
        message: "任务已开始",
      });
      getList();
      loadDashboardStats();
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg ? res.msg : "开始任务失败",
      });
    }
  } catch (error) {
    uni.showToast({
      title: "开始任务失败",
      icon: "error",
    });
  } finally {
    uni.hideLoading();
  }
};

const handleFinish = (item: SampleOrder) => {
  // 保存当前任务项
  currentTaskItem.value = item;

  // 重置表单数据
  finishForm.laboratoryCode = "";
  finishForm.remark = "";

  // 显示弹窗
  finishDialog.value.open();
};

// 弹窗确认处理
const handleFinishConfirm = async () => {
  // 验证必填字段
  if (!finishForm.laboratoryCode.trim()) {
    uToastRef.value.show({
      type: "error",
      message: "请输入实验室编号",
    });
    return;
  }

  if (!currentTaskItem.value) {
    uToastRef.value.show({
      type: "error",
      message: "任务信息丢失，请重试",
    });
    return;
  }

  try {
    uni.showLoading({ title: "处理中..." });

    const res = await uni.$u.http.post(
      "/software/engineerSampleOrder/updateStatus",
      {
        id: currentTaskItem.value.id,
        status: 2,
        laboratoryCode: finishForm.laboratoryCode.trim(),
        remark: finishForm.remark.trim(),
      }
    );

    if (res.code === 200) {
      // 只有成功时才关闭弹窗
      finishDialog.value.close();

      uToastRef.value.show({
        type: "success",
        message: "任务已完成",
      });

      // 刷新数据
      getList();
      loadDashboardStats();
    } else {
      // 请求失败时不关闭弹窗，显示错误信息
      uToastRef.value.show({
        type: "error",
        message: res.msg ? res.msg : "完成任务失败",
      });
    }
  } catch (error) {
    // 异常时不关闭弹窗，显示错误信息
    uToastRef.value.show({
      type: "error",
      message: "完成任务失败",
    });
  } finally {
    uni.hideLoading();
  }
};

// 弹窗取消处理
const handleFinishCancel = () => {
  finishDialog.value.close();
  currentTaskItem.value = null;
};

// 更新实验室编码处理
const handleUpdateLaboratoryCode = (item: SampleOrder) => {
  // 保存当前任务项
  currentUpdateLaboratoryCodeRow.value = item;

  // 回显数据 - 从当前任务项中获取实验室编码和备注
  updateLaboratoryCodeForm.laboratoryCode = item.laboratoryCode || "";
  updateLaboratoryCodeForm.remark = item.remark || "";

  // 显示弹窗
  updateLaboratoryCodeDialog.value.open();
};

// 更新实验室编码确认处理
const handleUpdateLaboratoryCodeConfirm = async () => {
  // 验证必填字段
  if (!updateLaboratoryCodeForm.laboratoryCode.trim()) {
    uToastRef.value.show({
      type: "error",
      message: "请输入实验室编号",
    });
    return;
  }

  if (!currentUpdateLaboratoryCodeRow.value) {
    uToastRef.value.show({
      type: "error",
      message: "任务信息丢失，请重试",
    });
    return;
  }

  try {
    uni.showLoading({ title: "处理中..." });

    const res = await uni.$u.http.post(
      "/software/engineerSampleOrder/updateStatus",
      {
        id: currentUpdateLaboratoryCodeRow.value.id, // 选中的id
        status: currentUpdateLaboratoryCodeRow.value.completionStatus, // 保持当前状态
        laboratoryCode: updateLaboratoryCodeForm.laboratoryCode.trim(), // 编码
        remark: updateLaboratoryCodeForm.remark.trim(), // 备注
      }
    );

    if (res.code === 200) {
      // 只有成功时才关闭弹窗
      updateLaboratoryCodeDialog.value.close();

      uToastRef.value.show({
        type: "success",
        message: "修改成功",
      });

      // 刷新数据
      getList();
      loadDashboardStats();
    } else {
      // 请求失败时不关闭弹窗，显示错误信息
      uToastRef.value.show({
        type: "error",
        message: res.msg ? res.msg : "修改失败",
      });
    }
  } catch (error) {
    // 异常时不关闭弹窗，显示错误信息
    uToastRef.value.show({
      type: "error",
      message: "修改失败",
    });
  } finally {
    uni.hideLoading();
  }
};

// 更新实验室编码取消处理
const handleUpdateLaboratoryCodeCancel = () => {
  updateLaboratoryCodeDialog.value.close();
  currentUpdateLaboratoryCodeRow.value = null;
};

// 更换工程师处理
const handleChangeEngineer = (item: SampleOrder) => {
  changeEngineerDialogRef.value?.open(item);
};

// 更换工程师确认处理
const handleChangeEngineerConfirm = (data: any) => {
  // 刷新数据
  getList();
  loadDashboardStats();
};

// 更换工程师关闭处理
const handleChangeEngineerClose = () => {
  // 可以在这里添加关闭时的逻辑
};

// 驳回处理
const handleReject = (item: SampleOrder) => {
  rejectDialogRef.value?.open(item);
};

// 驳回确认处理
const handleRejectConfirm = (data: any) => {
  // 刷新数据
  getList();
  loadDashboardStats();
};

// 驳回关闭处理
const handleRejectClose = () => {
  // 可以在这里添加关闭时的逻辑
};

// 逾期操作处理
const handleOverdueOperation = (item: SampleOrder) => {
  // 保存当前逾期任务项
  currentOverdueRow.value = item;

  // 重置表单数据
  overdueForm.expectedSampleTime = "";
  overdueForm.reasonForNoSample = "";
  overdueForm.solution = "";

  // 显示弹窗
  overdueDialog.value.open();
};

// 逾期操作确认处理
const handleOverdueConfirm = async () => {
  if (!currentOverdueRow.value) {
    uToastRef.value.show({
      type: "error",
      message: "任务信息丢失，请重试",
    });
    return;
  }

  try {
    uni.showLoading({ title: "处理中..." });

    const res = await uni.$u.http.post(
      "/software/engineerSampleOrder/updateStatus",
      {
        id: currentOverdueRow.value.id,
        status: "11", // 特殊处理，只更新"逾期情况"填写的字段
        expectedSampleTime: overdueForm.expectedSampleTime.trim() || null,
        reasonForNoSample: overdueForm.reasonForNoSample.trim() || null,
        solution: overdueForm.solution.trim() || null,
      }
    );

    if (res.code === 200) {
      // 只有成功时才关闭弹窗
      overdueDialog.value.close();

      uToastRef.value.show({
        type: "success",
        message: "逾期操作处理成功",
      });

      // 刷新数据
      getList();
      loadDashboardStats();
    } else {
      // 请求失败时不关闭弹窗，显示错误信息
      uToastRef.value.show({
        type: "error",
        message: res.msg ? res.msg : "逾期操作处理失败",
      });
    }
  } catch (error) {
    // 异常时不关闭弹窗，显示错误信息
    uToastRef.value.show({
      type: "error",
      message: "逾期操作处理失败",
    });
  } finally {
    uni.hideLoading();
  }
};

// 逾期操作取消处理
const handleOverdueCancel = () => {
  overdueDialog.value.close();
  currentOverdueRow.value = null;
};

// 批次管理处理
const handleBatchManagement = (item: SampleOrder) => {
  if (!item.id) {
    uToastRef.value.show({
      type: "error",
      message: "任务信息丢失，请重试",
    });
    return;
  }

  // 跳转到批次管理页面，传递打样单ID
  uni.navigateTo({
    url: `/pagesSoftware/batchManagement?sampleOrderId=${item.id}`,
  });
};

const getStatusType = (status: number): string => {
  const typeMap: Record<number, string> = {
    0: "info",
    1: "primary",
    2: "success",
    3: "warning",
    4: "warning",
  };
  return typeMap[status] || "default";
};

const formatDate = (dateStr?: string): string => {
  if (!dateStr) return "";
  return dayjs(dateStr).format("YYYY-MM-DD");
};

// 判断任务是否逾期
const isOverdueTask = (item: SampleOrder): boolean => {
  const currentDate = dayjs();
  const endDate = dayjs(item.endDate);

  if (item.completionStatus === 2) {
    // 已完成。实际完成时间 > 截止日期
    if (item.actualFinishTime) {
      const actualFinishTime = dayjs(item.actualFinishTime);
      if (actualFinishTime.isAfter(endDate)) {
        return true;
      }
    }
  } else if (item.completionStatus === 1 || item.completionStatus === 0) {
    // 进行中,未开始。当前时间 > 截止日期
    if (currentDate.isAfter(endDate)) {
      return true;
    }
  }

  return false;
};

// 生命周期
onMounted(async () => {
  // 加载数据
  await Promise.all([getList(), loadDashboardStats()]);
});

// 下拉刷新
onPullDownRefresh(() => {
  handleRefresh();
});
</script>

<style lang="scss" scoped>
.sample-order-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 统计概览样式
.stats-section {
  margin-bottom: 20rpx;
}

.stats-grid {
  display: flex;
  gap: 20rpx;
}

.stats-card {
  flex: 1;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }

  .stats-number {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #303133;
    margin-bottom: 8rpx;
  }

  .stats-title {
    display: block;
    font-size: 24rpx;
    color: #909399;
  }

  &.total {
    border-top: 6rpx solid #3a6993;
  }

  &.in-progress {
    border-top: 6rpx solid #409eff;
  }

  &.completed {
    border-top: 6rpx solid #67c23a;
  }

  &.overdue {
    border-top: 6rpx solid #f67777;
  }
}

// 筛选条件样式
.filter-section {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);

  .filter-item {
    margin-bottom: 24rpx;
  }

  .filter-buttons {
    display: flex;
    gap: 20rpx;
    margin-top: 20rpx;
  }
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  .toolbar-left {
    display: flex;
    align-items: center;
  }
}

// 任务列表样式
.task-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;

  .loading-tip {
    padding: 80rpx 40rpx;
    text-align: center;
    color: #909399;
    font-size: 28rpx;
  }

  .empty-tip {
    padding: 80rpx 40rpx;
    text-align: center;
    color: #909399;
    font-size: 28rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .code-text {
    color: #409eff;
    font-weight: bold;
    text-decoration: underline;
    cursor: pointer;
  }

  .action-buttons {
    display: flex;
    gap: 10rpx;
    align-items: center;
    flex-wrap: wrap;

    .detail-btn {
      color: #909399;
      font-size: 24rpx;
      text-decoration: underline;
      cursor: pointer;
      margin-left: 10rpx;
    }
  }
}

// 完成任务弹窗样式
.finish-dialog {
  background: white;
  border-radius: 16rpx;
  width: 600rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);

  .dialog-header {
    padding: 32rpx 32rpx 0;
    text-align: center;

    .dialog-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #303133;
    }
  }

  .dialog-content {
    padding: 32rpx;

    .finish-form {
      .form-item {
        margin-bottom: 24rpx;

        .form-label {
          display: block;
          font-size: 28rpx;
          color: #303133;
          margin-bottom: 12rpx;
          font-weight: 500;

          .required {
            color: #f56c6c;
            margin-left: 4rpx;
          }
        }
      }
    }
  }

  .dialog-footer {
    padding: 0 32rpx 32rpx;
    display: flex;
    gap: 20rpx;
  }
}

// 详情弹窗样式
.detail-dialog {
  background: white;
  border-radius: 16rpx;
  width: 680rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);

  .dialog-header {
    padding: 32rpx 32rpx 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2rpx solid #f0f0f0;
    padding-bottom: 24rpx;
    margin-bottom: 24rpx;

    .dialog-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #303133;
    }

    .close-btn {
      font-size: 32rpx;
      color: #909399;
      cursor: pointer;
      padding: 8rpx;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f5f5f5;
        color: #606266;
      }
    }
  }

  .dialog-content {
    padding: 0 32rpx 32rpx;
    max-height: 60vh;
    overflow-y: auto;

    .detail-info {
      .info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 24rpx;
        padding: 16rpx;
        background-color: #fafafa;
        border-radius: 12rpx;
        border-left: 6rpx solid #409eff;

        &:last-child {
          margin-bottom: 0;
        }

        .info-icon {
          font-size: 32rpx;
          margin-right: 16rpx;
          flex-shrink: 0;
          line-height: 1.2;
        }

        .info-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 8rpx;

          .info-label {
            font-size: 24rpx;
            color: #909399;
            font-weight: 500;
          }

          .info-value {
            font-size: 28rpx;
            color: #303133;
            font-weight: 600;
            word-break: break-all;
          }
        }
      }
    }
  }
}

// 逾期操作弹窗样式
.overdue-dialog {
  background: white;
  border-radius: 16rpx;
  width: 600rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);

  .dialog-header {
    padding: 32rpx 32rpx 0;
    text-align: center;

    .dialog-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #303133;
    }
  }

  .dialog-content {
    padding: 32rpx;

    .overdue-form {
      .form-item {
        margin-bottom: 24rpx;

        .form-label {
          display: block;
          font-size: 28rpx;
          color: #303133;
          margin-bottom: 12rpx;
          font-weight: 500;
        }

        .form-value {
          display: block;
          font-size: 28rpx;
          color: #409eff;
          font-weight: 600;
          padding: 12rpx 0;
          border-bottom: 2rpx solid #f0f0f0;
        }
      }
    }
  }

  .dialog-footer {
    padding: 0 32rpx 32rpx;
    display: flex;
    gap: 20rpx;
  }
}
</style>
